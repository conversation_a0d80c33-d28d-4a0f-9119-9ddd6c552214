<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.quarterlyassessments.mapper.MonthlyAssessmentStaffMapper">
    
    <resultMap type="MonthlyAssessmentStaff" id="MonthlyAssessmentStaffResult">
        <result property="id"                     column="id"                     />
        <result property="monthlyAssessmentId"    column="monthly_assessment_id"  />
        <result property="userId"                 column="user_id"                />
        <result property="roleId"                 column="role_id"                />
        <result property="createdAt"              column="created_at"             />
        <result property="updatedAt"              column="updated_at"             />
    </resultMap>

    <resultMap id="MonthlyAssessmentStaffWithUserResult" type="MonthlyAssessmentStaff" extends="MonthlyAssessmentStaffResult">
        <association property="user" javaType="SysUser">
            <id property="userId" column="user_id" />
            <result property="userName" column="sys_user_name" />
            <result property="nickName" column="sys_nick_name" />
            <result property="email" column="email" />
            <result property="phonenumber" column="phonenumber" />
            <result property="status" column="user_status" />
        </association>
        <association property="role" javaType="SysRole">
            <id property="roleId" column="role_id" />
            <result property="roleName" column="role_name" />
            <result property="roleKey" column="role_key" />
            <result property="status" column="role_status" />
        </association>
    </resultMap>

    <sql id="selectMonthlyAssessmentStaffVo">
        select id, monthly_assessment_id, user_id, role_id, created_at, updated_at 
        from monthly_assessment_staff
    </sql>

    <select id="selectMonthlyAssessmentStaffList" parameterType="MonthlyAssessmentStaff" resultMap="MonthlyAssessmentStaffResult">
        <include refid="selectMonthlyAssessmentStaffVo"/>
        <where>  
            <if test="monthlyAssessmentId != null and monthlyAssessmentId != ''">and monthly_assessment_id = #{monthlyAssessmentId}</if>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="roleId != null">and role_id = #{roleId}</if>
        </where>
        order by created_at desc
    </select>
    
    <select id="selectMonthlyAssessmentStaffById" parameterType="String" resultMap="MonthlyAssessmentStaffWithUserResult">
        select mas.id, mas.monthly_assessment_id, mas.user_id, mas.role_id, 
               mas.created_at, mas.updated_at,
               u.user_name as sys_user_name, u.nick_name as sys_nick_name, u.email, u.phonenumber, u.status as user_status,
               r.role_name, r.role_key, r.status as role_status
        from monthly_assessment_staff mas
        left join sys_user u on mas.user_id = u.user_id
        left join sys_role r on mas.role_id = r.role_id
        where mas.id = #{id}
    </select>

    <insert id="insertMonthlyAssessmentStaff" parameterType="MonthlyAssessmentStaff">
        insert into monthly_assessment_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="monthlyAssessmentId != null">monthly_assessment_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="roleId != null">role_id,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="monthlyAssessmentId != null">#{monthlyAssessmentId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="roleId != null">#{roleId},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateMonthlyAssessmentStaff" parameterType="MonthlyAssessmentStaff">
        update monthly_assessment_staff
        <trim prefix="SET" suffixOverrides=",">
            <if test="monthlyAssessmentId != null">monthly_assessment_id = #{monthlyAssessmentId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonthlyAssessmentStaffById" parameterType="String">
        delete from monthly_assessment_staff where id = #{id}
    </delete>

    <delete id="deleteMonthlyAssessmentStaffByIds" parameterType="String">
        delete from monthly_assessment_staff where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByMonthlyAssessmentId" parameterType="String">
        delete from monthly_assessment_staff where monthly_assessment_id = #{monthlyAssessmentId}
    </delete>

    <delete id="deleteMonthlyAssessmentStaffByMonthlyId" parameterType="String">
        delete from monthly_assessment_staff where monthly_assessment_id = #{monthlyAssessmentId}
    </delete>

    <insert id="batchInsertMonthlyAssessmentStaff" parameterType="java.util.List">
        insert into monthly_assessment_staff (id, monthly_assessment_id, user_id, role_id, created_at) values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.monthlyAssessmentId}, #{item.userId}, #{item.roleId}, #{item.createdAt})
        </foreach>
    </insert>
    
    <delete id="deleteMonthlyAssessmentStaffByMonthlyIdAndRoleId">
        delete from monthly_assessment_staff 
        where monthly_assessment_id = #{monthlyAssessmentId} 
        and role_id = #{roleId}
    </delete>
    
</mapper> 