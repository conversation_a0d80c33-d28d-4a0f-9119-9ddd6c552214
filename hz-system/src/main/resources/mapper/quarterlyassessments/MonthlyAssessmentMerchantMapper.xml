<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.quarterlyassessments.mapper.MonthlyAssessmentMerchantMapper">
    
    <resultMap type="MonthlyAssessmentMerchant" id="MonthlyAssessmentMerchantResult">
        <result property="id"                    column="id"                    />
        <result property="monthlyAssessmentId"   column="monthly_assessment_id" />
        <result property="merchantId"            column="merchant_id"           />
        <result property="selectionType"         column="selection_type"        />
        <result property="selectionOrder"        column="selection_order"       />
        <result property="distanceToNext"        column="distance_to_next"      />
        <result property="assessmentStatus"      column="assessment_status"     />
        <result property="assessmentScore"       column="assessment_score"      />
        <result property="assessmentDate"        column="assessment_date"       />
        <result property="assessorId"            column="assessor_id"           />
        <result property="zhuanmaiCheckStatus"   column="zhuanmai_check_status"  />
        <result property="zhuanmaiCheckInTime"   column="zhuanmai_check_in_time" />
        <result property="zhuanmaiCheckOutTime"  column="zhuanmai_check_out_time"/>
        <result property="yingxiaoCheckStatus"   column="yingxiao_check_status"  />
        <result property="yingxiaoCheckInTime"   column="yingxiao_check_in_time" />
        <result property="yingxiaoCheckOutTime"  column="yingxiao_check_out_time"/>
        <result property="createId"              column="create_id"             />
        <result property="createBy"              column="create_by"             />
        <result property="createTime"            column="create_time"           />
        <result property="updateId"              column="update_id"             />
        <result property="updateBy"              column="update_by"             />
        <result property="updateTime"            column="update_time"           />
        <result property="remark"                column="remark"                />
    </resultMap>

    <resultMap id="MonthlyAssessmentMerchantWithMerchantResult" type="MonthlyAssessmentMerchant" extends="MonthlyAssessmentMerchantResult">
        <result property="assessorName"          column="assessor_name"         />
        <association property="merchantInfo" javaType="MerchantInfo">
            <id property="id" column="merchant_info_id" />
            <result property="creatId" column="merchant_creat_id" />
            <result property="creatBy" column="merchant_creat_by" />
            <result property="creatTime" column="merchant_creat_time" />
            <result property="updateId" column="merchant_update_id" />
            <result property="updateBy" column="merchant_update_by" />
            <result property="updateTime" column="merchant_update_time" />
            <result property="remark" column="merchant_remark" />
            <result property="provinces" column="provinces" />
            <result property="licence" column="licence" />
            <result property="merchantName" column="merchant_name" />
            <result property="legalName" column="legal_name" />
            <result property="merchantStatue" column="merchant_statue" />
            <result property="phoneNumber" column="phone_number" />
            <result property="county" column="county" />
            <result property="countyId" column="county_id" />
            <result property="shichangbu" column="shichangbu" />
            <result property="yingxiaoxian" column="yingxiaoxian" />
            <result property="businessAddress" column="business_address" />
            <result property="address" column="address" />
            <result property="businessScope" column="business_scope" />
            <result property="marketType" column="market_type" />
            <result property="marketTypeSegment" column="market_type_segment" />
            <result property="yetai" column="yetai" />
            <result property="jingyingguimo" column="jingyingguimo" />
            <result property="shangquan" column="shangquan" />
            <result property="dinghuozhouqileixing" column="dinghuozhouqileixing" />
            <result property="dinghuori" column="dinghuori" />
            <result property="dinghuofangshi" column="dinghuofangshi" />
            <result property="jiesuanfangshi" column="jiesuanfangshi" />
            <result property="wangshangjiesuan" column="wangshangjiesuan" />
            <result property="chengxindengji" column="chengxindengji" />
            <result property="dangweibianma" column="dangweibianma" />
            <result property="dangwei" column="dangwei" />
            <result property="ruwangriqi" column="ruwangriqi" />
            <result property="zhongduancengji" column="zhongduancengji" />
            <result property="zhongduanleibie" column="zhongduanleibie" />
            <result property="zhongduanleibiexifen" column="zhongduanleibiexifen" />
            <result property="xuejiadangwei" column="xuejiadangwei" />
            <result property="xuejiayanzhongduanleixing" column="xuejiayanzhongduanleixing" />
            <result property="creatId" column="creat_id" />
            <result property="creatBy" column="creat_by" />
            <result property="photo" column="photo" />
            <result property="creatTime" column="creat_time" />
            <result property="originalLongitude" column="original_longitude" />
            <result property="originalLatitude" column="original_latitude" />
            <result property="longitudeAfterOffset" column="longitude_after_offset" />
            <result property="latitudeAfterOffset" column="latitude_after_offset" />
            <result property="lastAssessedDate" column="last_assessed_date" />
        </association>
        <collection property="examinationPaperList"   javaType="java.util.List"  resultMap="ExaminationPaperResult" />
    </resultMap>

    <resultMap id="ExaminationPaperResult" type="ExaminationPaper">
        <result property="id"    column="paper_id"    />
        <result property="name"    column="paper_name"    />
        <result property="type"    column="paper_type"    />
        <result property="sumScore"    column="paper_score"    />
    </resultMap>

    <resultMap id="MonthlyMerchantAssessmentDtoResult" type="com.hz.quarterlyassessments.domain.dto.MonthlyMerchantAssessmentDto">
        <result property="merchantId"        column="merchant_id"         />
        <result property="merchantName"       column="merchant_name"       />
        <result property="selectionType"     column="selection_type"      />
        <result property="legalName"         column="legal_name"          />
        <result property="phoneNumber"       column="phone_number"        />
        <result property="county"            column="county"              />
        <result property="assessmentStatus"  column="assessment_status"   />
        <result property="zhuanmaiCheckStatus" column="zhuanmai_check_status" />
        <result property="yingxiaoCheckStatus" column="yingxiao_check_status" />
        <result property="photo"             column="photo"               />
        <result property="businessAddress"   column="business_address"    />
        <result property="remark"            column="remark"              />
        <result property="zhongduancengji"   column="zhongduancengji"     />
        <collection property="examinationPaperList"   javaType="java.util.List"  resultMap="ExaminationPaperResult" />
    </resultMap>

    <sql id="selectMonthlyAssessmentMerchantVo">
        select id, monthly_assessment_id, merchant_id, selection_type, selection_order, 
               distance_to_next, assessment_status, assessment_score, assessment_date, assessor_id,
               zhuanmai_check_status, zhuanmai_check_in_time, zhuanmai_check_out_time,
               yingxiao_check_status, yingxiao_check_in_time, yingxiao_check_out_time,
               create_id, create_by, create_time, update_id, update_by, update_time, remark 
        from monthly_assessment_merchants
    </sql>

    <select id="selectMonthlyAssessmentMerchantList" parameterType="MonthlyAssessmentMerchant" resultMap="MonthlyAssessmentMerchantResult">
        <include refid="selectMonthlyAssessmentMerchantVo"/>
        <where>  
            <if test="monthlyAssessmentId != null">and monthly_assessment_id = #{monthlyAssessmentId}</if>
            <if test="merchantId != null">and merchant_id = #{merchantId}</if>
            <if test="selectionType != null and selectionType != ''">and selection_type = #{selectionType}</if>
            <if test="assessmentStatus != null and assessmentStatus != ''">and assessment_status = #{assessmentStatus}</if>
            <if test="assessorId != null">and assessor_id = #{assessorId}</if>
        </where>
        order by monthly_assessment_id, selection_order
    </select>
    
    <select id="selectMonthlyAssessmentMerchantById" parameterType="Long" resultMap="MonthlyAssessmentMerchantWithMerchantResult">
        select mam.id, mam.monthly_assessment_id, mam.merchant_id, mam.selection_type, mam.selection_order,
               mam.distance_to_next, mam.assessment_status, mam.assessment_score, mam.assessment_date, mam.assessor_id,
               mam.zhuanmai_check_status, mam.zhuanmai_check_in_time, mam.zhuanmai_check_out_time,
               mam.yingxiao_check_status, mam.yingxiao_check_in_time, mam.yingxiao_check_out_time,
               mam.create_id, mam.create_by, mam.create_time, mam.update_id, mam.update_by, mam.update_time, mam.remark,
               mi.id as merchant_info_id, mi.creat_id as merchant_creat_id, mi.creat_by as merchant_creat_by, 
               mi.creat_time as merchant_creat_time, mi.update_id as merchant_update_id, mi.update_by as merchant_update_by, 
               mi.update_time as merchant_update_time, mi.remark as merchant_remark, mi.provinces, mi.licence, 
               mi.merchant_name, mi.legal_name, mi.merchant_statue, mi.phone_number, mi.county, mi.county_id, 
               mi.shichangbu, mi.yingxiaoxian, mi.business_address, mi.business_scope, mi.market_type, 
               mi.market_type_segment, mi.yetai, mi.jingyingguimo, mi.shangquan, mi.dinghuozhouqileixing, 
               mi.dinghuori, mi.dinghuofangshi, mi.jiesuanfangshi, mi.wangshangjiesuan, mi.chengxindengji, 
               mi.dangweibianma, mi.dangwei, mi.ruwangriqi, mi.zhongduancengji, mi.zhongduanleibie, 
               mi.zhongduanleibiexifen, mi.xuejiadangwei, mi.xuejiayanzhongduanleixing, mi.creat_id, 
               mi.creat_by, mi.photo, mi.creat_time, mi.last_assessed_date,
               mi.original_longitude, mi.original_latitude, mi.longitude_after_offset, mi.latitude_after_offset, mi.address,
               su.nick_name as assessor_name, ep.id as paper_id, ep.type as paper_type, ep.name as paper_name, erm.score as paper_score
        from monthly_assessment_merchants mam
        left join merchant_info mi on mam.merchant_id = mi.id
        left join sys_user su on mam.assessor_id = su.user_id
        left join examination_paper ep on ep.monthly_id = mam.monthly_assessment_id and ep.brand = mi.zhongduancengji
        <if test="type != null">and ep.type = #{type}</if>
        left join (select paper_id, merchant_id, sum(score) score from examination_reply_merchant group by paper_id,merchant_id) erm on erm.paper_id = ep.id and erm.merchant_id = mam.merchant_id
        where mam.id = #{id}
    </select>

    <select id="selectMerchantsByMonthlyAssessmentId" resultMap="MonthlyAssessmentMerchantWithMerchantResult">
        select mam.id, mam.monthly_assessment_id, mam.merchant_id, mam.selection_type, mam.selection_order,
               mam.distance_to_next, mam.assessment_status, mam.assessment_score, mam.assessment_date, mam.assessor_id,
               mam.zhuanmai_check_status, mam.zhuanmai_check_in_time, mam.zhuanmai_check_out_time,
               mam.yingxiao_check_status, mam.yingxiao_check_in_time, mam.yingxiao_check_out_time,
               mam.create_id, mam.create_by, mam.create_time, mam.update_id, mam.update_by, mam.update_time, mam.remark,
               mi.id as merchant_info_id, mi.creat_id as merchant_creat_id, mi.creat_by as merchant_creat_by, 
               mi.creat_time as merchant_creat_time, mi.update_id as merchant_update_id, mi.update_by as merchant_update_by, 
               mi.update_time as merchant_update_time, mi.remark as merchant_remark, mi.provinces, mi.licence, 
               mi.merchant_name, mi.legal_name, mi.merchant_statue, mi.phone_number, mi.county, mi.county_id, 
               mi.shichangbu, mi.yingxiaoxian, mi.business_address, mi.business_scope, mi.market_type, 
               mi.market_type_segment, mi.yetai, mi.jingyingguimo, mi.shangquan, mi.dinghuozhouqileixing, 
               mi.dinghuori, mi.dinghuofangshi, mi.jiesuanfangshi, mi.wangshangjiesuan, mi.chengxindengji, 
               mi.dangweibianma, mi.dangwei, mi.ruwangriqi, mi.zhongduancengji, mi.zhongduanleibie, 
               mi.zhongduanleibiexifen, mi.xuejiadangwei, mi.xuejiayanzhongduanleixing, mi.creat_id, 
               mi.creat_by, mi.photo, mi.creat_time, mi.last_assessed_date,
               mi.original_longitude, mi.original_latitude, mi.longitude_after_offset, mi.latitude_after_offset, mi.address,
               su.nick_name as assessor_name
        from monthly_assessment_merchants mam
        left join merchant_info mi on mam.merchant_id = mi.id
        left join sys_user su on mam.assessor_id = su.user_id
        where mam.monthly_assessment_id = #{monthlyAssessmentId}
        <if test="selectionType != null">and selection_type = #{selectionType}</if>
        order by mam.selection_order
    </select>

    <insert id="insertMonthlyAssessmentMerchant" parameterType="MonthlyAssessmentMerchant" useGeneratedKeys="true" keyProperty="id">
        insert into monthly_assessment_merchants
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monthlyAssessmentId != null">monthly_assessment_id,</if>
            <if test="merchantId != null">merchant_id,</if>
            <if test="selectionType != null">selection_type,</if>
            <if test="selectionOrder != null">selection_order,</if>
            <if test="distanceToNext != null">distance_to_next,</if>
            <if test="assessmentStatus != null">assessment_status,</if>
            <if test="assessmentScore != null">assessment_score,</if>
            <if test="assessmentDate != null">assessment_date,</if>
            <if test="assessorId != null">assessor_id,</if>
            <if test="zhuanmaiCheckStatus != null">zhuanmai_check_status,</if>
            <if test="zhuanmaiCheckInTime != null">zhuanmai_check_in_time,</if>
            <if test="zhuanmaiCheckOutTime != null">zhuanmai_check_out_time,</if>
            <if test="yingxiaoCheckStatus != null">yingxiao_check_status,</if>
            <if test="yingxiaoCheckInTime != null">yingxiao_check_in_time,</if>
            <if test="yingxiaoCheckOutTime != null">yingxiao_check_out_time,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monthlyAssessmentId != null">#{monthlyAssessmentId},</if>
            <if test="merchantId != null">#{merchantId},</if>
            <if test="selectionType != null">#{selectionType},</if>
            <if test="selectionOrder != null">#{selectionOrder},</if>
            <if test="distanceToNext != null">#{distanceToNext},</if>
            <if test="assessmentStatus != null">#{assessmentStatus},</if>
            <if test="assessmentScore != null">#{assessmentScore},</if>
            <if test="assessmentDate != null">#{assessmentDate},</if>
            <if test="assessorId != null">#{assessorId},</if>
            <if test="zhuanmaiCheckStatus != null">#{zhuanmaiCheckStatus},</if>
            <if test="zhuanmaiCheckInTime != null">#{zhuanmaiCheckInTime},</if>
            <if test="zhuanmaiCheckOutTime != null">#{zhuanmaiCheckOutTime},</if>
            <if test="yingxiaoCheckStatus != null">#{yingxiaoCheckStatus},</if>
            <if test="yingxiaoCheckInTime != null">#{yingxiaoCheckInTime},</if>
            <if test="yingxiaoCheckOutTime != null">#{yingxiaoCheckOutTime},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertMonthlyAssessmentMerchants">
        insert into monthly_assessment_merchants(monthly_assessment_id, merchant_id, selection_type, selection_order, 
                                                 distance_to_next, assessment_status, zhuanmai_check_status, yingxiao_check_status,
                                                 create_id, create_by, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.monthlyAssessmentId}, #{item.merchantId}, #{item.selectionType}, #{item.selectionOrder}, 
             #{item.distanceToNext}, #{item.assessmentStatus}, 
             COALESCE(#{item.zhuanmaiCheckStatus}, '0'), COALESCE(#{item.yingxiaoCheckStatus}, '0'),
             #{item.createId}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateMonthlyAssessmentMerchant" parameterType="MonthlyAssessmentMerchant">
        update monthly_assessment_merchants
        <trim prefix="SET" suffixOverrides=",">
            <if test="monthlyAssessmentId != null">monthly_assessment_id = #{monthlyAssessmentId},</if>
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="selectionType != null">selection_type = #{selectionType},</if>
            <if test="selectionOrder != null">selection_order = #{selectionOrder},</if>
            <if test="distanceToNext != null">distance_to_next = #{distanceToNext},</if>
            <if test="assessmentStatus != null">assessment_status = #{assessmentStatus},</if>
            <if test="assessmentScore != null">assessment_score = #{assessmentScore},</if>
            <if test="assessmentDate != null">assessment_date = #{assessmentDate},</if>
            <if test="assessorId != null">assessor_id = #{assessorId},</if>
            <if test="zhuanmaiCheckStatus != null">zhuanmai_check_status = #{zhuanmaiCheckStatus},</if>
            <if test="zhuanmaiCheckInTime != null">zhuanmai_check_in_time = #{zhuanmaiCheckInTime},</if>
            <if test="zhuanmaiCheckOutTime != null">zhuanmai_check_out_time = #{zhuanmaiCheckOutTime},</if>
            <if test="yingxiaoCheckStatus != null">yingxiao_check_status = #{yingxiaoCheckStatus},</if>
            <if test="yingxiaoCheckInTime != null">yingxiao_check_in_time = #{yingxiaoCheckInTime},</if>
            <if test="yingxiaoCheckOutTime != null">yingxiao_check_out_time = #{yingxiaoCheckOutTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonthlyAssessmentMerchantById" parameterType="Long">
        delete from monthly_assessment_merchants where id = #{id}
    </delete>

    <delete id="deleteMonthlyAssessmentMerchantByIds" parameterType="String">
        delete from monthly_assessment_merchants where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByMonthlyAssessmentId" parameterType="String">
        delete from monthly_assessment_merchants where monthly_assessment_id = #{monthlyAssessmentId}
    </delete>

    <delete id="deleteByQuarterlyAssessmentId" parameterType="String">
        delete from monthly_assessment_merchants where monthly_assessment_id in (
            select id from monthly_assessments where quarterly_assessment_id = #{quarterlyAssessmentId}
        )
    </delete>

    <select id="countMerchantsByMonthlyAssessmentId" parameterType="String" resultType="int">
        select count(*) from monthly_assessment_merchants where monthly_assessment_id = #{monthlyAssessmentId}
    </select>

    <select id="checkMerchantAssociation" resultType="int">
        select count(*) from monthly_assessment_merchants 
        where monthly_assessment_id = #{monthlyAssessmentId} and merchant_id = #{merchantId}
    </select>

    <!-- 通用状态更新 - 专卖 -->
    <update id="updateZhuanmaiCheckStatus">
        update monthly_assessment_merchants 
        set zhuanmai_check_status = #{zhuanmaiCheckStatus},
            <if test="zhuanmaiCheckInTime != null">zhuanmai_check_in_time = #{zhuanmaiCheckInTime},</if>
            <if test="zhuanmaiCheckOutTime != null">zhuanmai_check_out_time = #{zhuanmaiCheckOutTime},</if>
            update_id = #{updateId},
            update_by = #{updateBy},
            update_time = #{updateTime}
        where id = #{id}
    </update>

    <!-- 通用状态更新 - 营销 -->
    <update id="updateYingxiaoCheckStatus">
        update monthly_assessment_merchants 
        set yingxiao_check_status = #{yingxiaoCheckStatus},
            <if test="yingxiaoCheckInTime != null">yingxiao_check_in_time = #{yingxiaoCheckInTime},</if>
            <if test="yingxiaoCheckOutTime != null">yingxiao_check_out_time = #{yingxiaoCheckOutTime},</if>
            update_id = #{updateId},
            update_by = #{updateBy},
            update_time = #{updateTime}
        where id = #{id}
    </update>



    <!-- 更新商户闭店状态 -->
    <update id="updateAssessmentStatus">
        update monthly_assessment_merchants 
        set assessment_status = #{assessmentStatus}
        where id = #{id}
    </update>



    <select id="selectMerchantAssessmentsSummaryByMonthlyId" resultMap="MonthlyMerchantAssessmentDtoResult">
        select
            mam.merchant_id,
            mi.merchant_name,
            mam.selection_type,
            mi.legal_name,
            mi.phone_number,
            mi.county,
            mam.assessment_status,
            mam.zhuanmai_check_status,
            mam.yingxiao_check_status,
            mi.photo,
            mi.business_address,
            mam.remark,
            mi.zhongduancengji,
            ep.id as paper_id, ep.type as paper_type, ep.name as paper_name, erm.score as paper_score
        from monthly_assessment_merchants mam
        left join merchant_info mi on mam.merchant_id = mi.id
        left join examination_paper ep on ep.monthly_id = mam.monthly_assessment_id and ep.brand = mi.zhongduancengji
        left join (select paper_id, merchant_id, sum(score) score from examination_reply_merchant group by paper_id,merchant_id) erm on erm.paper_id = ep.id and erm.merchant_id = mam.merchant_id
        where mam.monthly_assessment_id = #{monthlyAssessmentId}
        order by mam.selection_order
    </select>



    <!-- 根据月度考核ID统计专卖和营销都签退（状态都为3）的商户数量（排除无证户和闭店商户） -->
    <select id="countBothCheckStatusThreeByMonthlyAssessmentId" parameterType="String" resultType="int">
        select count(*)
        from monthly_assessment_merchants
        where monthly_assessment_id = #{monthlyAssessmentId}
        and zhuanmai_check_status = '3'
        and yingxiao_check_status = '3'
        and selection_type != '2'
        and assessment_status != '1'
    </select>

</mapper> 