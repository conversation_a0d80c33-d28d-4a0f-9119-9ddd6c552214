<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.examination.mapper.ExaminationPaperMapper">
    
    <resultMap type="ExaminationPaper" id="ExaminationPaperResult">
        <result property="id"    column="id"    />
        <result property="monthlyId"    column="monthly_id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="typeName"    column="type_name"    />
        <result property="brand"    column="brand"    />
        <result property="brandName"    column="brand_name"    />
        <result property="status"    column="status"    />
        <result property="isDisable"    column="is_disable"    />
        <result property="sort"    column="sort"    />
        <result property="topicCount"    column="topicCount"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectExaminationPaperVo">
        select id, monthly_id, name, type, type_name, brand, brand_name, status, is_disable, sort, create_id, create_by, create_time, update_id, update_by, update_time, remark from examination_paper
    </sql>

    <select id="selectExaminationPaperList" parameterType="ExaminationPaper" resultMap="ExaminationPaperResult">
        <include refid="selectExaminationPaperVo"/>
        <where>
            <if test="monthlyId != null  and monthlyId != ''"> and monthly_id = #{monthlyId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="typeName != null  and typeName != ''"> and type_name like concat('%', #{typeName}, '%')</if>
            <if test="brand != null  and brand != ''"> and brand = #{brand}</if>
            <if test="brandName != null  and brandName != ''"> and brand_name like concat('%', #{brandName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="isDisable != null  and isDisable != ''"> and is_disable = #{isDisable}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectExaminationPaperListByMonthlyId" parameterType="String" resultMap="ExaminationPaperResult">
        select ep.id, ep.monthly_id, ep.name, ep.type, ep.type_name, ep.brand, ep.brand_name, ep.status, ep.is_disable, ep.sort, ep.create_id, ep.create_by, ep.create_time, ep.update_id, ep.update_by, ep.update_time, ep.remark, etc.topicCount
        from examination_paper ep left join (select paper_id,count(1) topicCount from examination_topic group by paper_id) etc ON etc.paper_id = ep.id
        where ep.monthly_id = #{monthlyId} order by ep.sort
    </select>

    <select id="selectExaminationPaperListByTypeAndBrand" parameterType="String" resultMap="ExaminationPaperResult">
        select ep.id, ep.monthly_id, ep.name, ep.type, ep.type_name, ep.brand, ep.brand_name, ep.status, ep.is_disable, ep.sort, ep.create_id, ep.create_by, ep.create_time, ep.update_id, ep.update_by, ep.update_time, ep.remark
        from examination_paper ep
        where ep.type = #{type} and ep.brand = #{brand} order by ep.monthly_id desc
    </select>

    <select id="selectExaminationPaperById" parameterType="Long" resultMap="ExaminationPaperResult">
        <include refid="selectExaminationPaperVo"/>
        where id = #{id}
    </select>

    <insert id="batchInsertExaminationPaper">
        insert into examination_paper( monthly_id, name, type, type_name, brand, brand_name, sort, create_id, create_by, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.monthlyId}, #{item.name}, #{item.type}, #{item.typeName}, #{item.brand}, #{item.brandName}, #{item.sort}, #{item.createId}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <insert id="insertExaminationPaper" parameterType="ExaminationPaper" useGeneratedKeys="true" keyProperty="id">
        insert into examination_paper
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monthlyId != null">monthly_id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="typeName != null">type_name,</if>
            <if test="brand != null">brand,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="status != null">status,</if>
            <if test="isDisable != null">is_disable,</if>
            <if test="sort != null">sort,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monthlyId != null">#{monthlyId},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="typeName != null">#{typeName},</if>
            <if test="brand != null">#{brand},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="status != null">#{status},</if>
            <if test="isDisable != null">#{isDisable},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateExaminationPaper" parameterType="ExaminationPaper">
        update examination_paper
        <trim prefix="SET" suffixOverrides=",">
            <if test="monthlyId != null">monthly_id = #{monthlyId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="typeName != null">type_name = #{typeName},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="brandName != null">brand_name = #{brandName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDisable != null">is_disable = #{isDisable},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExaminationPaperById" parameterType="Long">
        delete from examination_paper where id = #{id}
    </delete>

    <delete id="deleteExaminationPaperByMonthlyId" parameterType="String">
        delete from examination_paper where monthly_id = #{monthlyId}
    </delete>

    <delete id="deleteExaminationPaperByIds" parameterType="String">
        delete from examination_paper where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countExaminationPaperByQuarterlyAssessmentId" parameterType="String" resultType="int">
        select count(*)
        from examination_paper ep
        inner join monthly_assessments ma on ep.monthly_id = ma.id
        where ma.quarterly_assessment_id = #{quarterlyAssessmentId}
    </select>
</mapper>