package com.hz.merchant.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hz.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

/**
 * 商户信息对象 merchant_info
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public class MerchantInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 省 */
    @Excel(name = "省")
    private String provinces;

    /** 许可证号 */
    @Excel(name = "许可证号")
    private String licence;

    private String merchantCode;

    /** 商户名称 */
    @Excel(name = "商户名称")
    private String merchantName;

    /** 法人 */
    @Excel(name = "法人")
    private String legalName;

    /** 商户状态 */
    @Excel(name = "商户状态")
    private String merchantStatue;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phoneNumber;

    /** 县区 */
    @Excel(name = "县区")
    private String county;

    /** 县区id */
    @Excel(name = "县区id")
    private Long countyId;

    /** 市场部 */
    @Excel(name = "市场部")
    private String shichangbu;

    /** 营销线 */
    @Excel(name = "营销线")
    private String yingxiaoxian;

    /** 经营地址 */
    @Excel(name = "经营地址")
    private String businessAddress;


    /** 地址 */
    @Excel(name = "地址")
    private String address;
    /** 经营范围 */
    @Excel(name = "经营范围")
    private String businessScope;

    /** 市场类型 */
    @Excel(name = "市场类型")
    private String marketType;

    /** 市场类型细分 */
    @Excel(name = "市场类型细分")
    private String marketTypeSegment;

    /** 业态 */
    @Excel(name = "业态")
    private String yetai;

    /** 经营规模 */
    @Excel(name = "经营规模")
    private String jingyingguimo;

    /** 商圈 */
    @Excel(name = "商圈")
    private String shangquan;

    /** 订货周期类型 */
    @Excel(name = "订货周期类型")
    private String dinghuozhouqileixing;

    /** 订货日 */
    @Excel(name = "订货日")
    private String dinghuori;

    /** 订货方式 */
    @Excel(name = "订货方式")
    private String dinghuofangshi;

    /** 结算方式 */
    @Excel(name = "结算方式")
    private String jiesuanfangshi;

    /** 网上结算 */
    @Excel(name = "网上结算")
    private String wangshangjiesuan;

    /** 诚信等级 */
    @Excel(name = "诚信等级")
    private String chengxindengji;

    /** 档位编码 */
    @Excel(name = "档位编码")
    private Long dangweibianma;

    /** 档位 */
    @Excel(name = "档位")
    private String dangwei;

    /** 入网日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入网日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date ruwangriqi;

    /** 终端层级 */
    @Excel(name = "终端层级")
    private String zhongduancengji;

    /** 终端类别 */
    @Excel(name = "终端类别")
    private String zhongduanleibie;

    /** 终端类型细分 */
    @Excel(name = "终端类型细分")
    private String zhongduanleibiexifen;

    /** 雪茄烟档位 */
    @Excel(name = "雪茄烟档位")
    private String xuejiadangwei;

    /** 雪茄烟终端类型 */
    @Excel(name = "雪茄烟终端类型")
    private String xuejiayanzhongduanleixing;

    /** 创建人id */
    @Excel(name = "创建人id")
    private Long creatId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creatBy;

    /** 照片 */
    @Excel(name = "照片")
    private String photo;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date creatTime;

    /** 原始经度（WGS84） */
    @Excel(name = "原始经度")
    private Double originalLongitude;

    /** 原始纬度（WGS84） */
    @Excel(name = "原始纬度")
    private Double originalLatitude;

    /** 偏移后经度（GCJ-02，用于高德地图） */
    @Excel(name = "偏移后经度")
    private Double longitudeAfterOffset;

    /** 偏移后纬度（GCJ-02，用于高德地图） */
    @Excel(name = "偏移后纬度")
    private Double latitudeAfterOffset;

    /** 最后考核日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后考核日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastAssessedDate;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setProvinces(String provinces) 
    {
        this.provinces = provinces;
    }

    public String getProvinces() 
    {
        return provinces;
    }

    public void setLicence(String licence)
    {
        this.licence = licence;
    }

    public String getLicence()
    {
        return licence;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public void setMerchantName(String merchantName)
    {
        this.merchantName = merchantName;
    }

    public String getMerchantName() 
    {
        return merchantName;
    }

    public void setLegalName(String legalName) 
    {
        this.legalName = legalName;
    }

    public String getLegalName() 
    {
        return legalName;
    }

    public void setMerchantStatue(String merchantStatue) 
    {
        this.merchantStatue = merchantStatue;
    }

    public String getMerchantStatue() 
    {
        return merchantStatue;
    }

    public void setPhoneNumber(String phoneNumber) 
    {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneNumber() 
    {
        return phoneNumber;
    }

    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }

    public void setCountyId(Long countyId) 
    {
        this.countyId = countyId;
    }

    public Long getCountyId() 
    {
        return countyId;
    }

    public void setShichangbu(String shichangbu) 
    {
        this.shichangbu = shichangbu;
    }

    public String getShichangbu() 
    {
        return shichangbu;
    }

    public void setYingxiaoxian(String yingxiaoxian) 
    {
        this.yingxiaoxian = yingxiaoxian;
    }

    public String getYingxiaoxian() 
    {
        return yingxiaoxian;
    }

    public void setBusinessAddress(String businessAddress) 
    {
        this.businessAddress = businessAddress;
    }

    public String getBusinessAddress() 
    {
        return businessAddress;
    }

    public void setBusinessScope(String businessScope) 
    {
        this.businessScope = businessScope;
    }

    public String getBusinessScope() 
    {
        return businessScope;
    }

    public void setMarketType(String marketType) 
    {
        this.marketType = marketType;
    }

    public String getMarketType() 
    {
        return marketType;
    }

    public void setMarketTypeSegment(String marketTypeSegment) 
    {
        this.marketTypeSegment = marketTypeSegment;
    }

    public String getMarketTypeSegment() 
    {
        return marketTypeSegment;
    }

    public void setYetai(String yetai) 
    {
        this.yetai = yetai;
    }

    public String getYetai() 
    {
        return yetai;
    }

    public void setJingyingguimo(String jingyingguimo) 
    {
        this.jingyingguimo = jingyingguimo;
    }

    public String getJingyingguimo() 
    {
        return jingyingguimo;
    }

    public void setShangquan(String shangquan) 
    {
        this.shangquan = shangquan;
    }

    public String getShangquan() 
    {
        return shangquan;
    }

    public void setDinghuozhouqileixing(String dinghuozhouqileixing) 
    {
        this.dinghuozhouqileixing = dinghuozhouqileixing;
    }

    public String getDinghuozhouqileixing() 
    {
        return dinghuozhouqileixing;
    }

    public void setDinghuori(String dinghuori) 
    {
        this.dinghuori = dinghuori;
    }

    public String getDinghuori() 
    {
        return dinghuori;
    }

    public void setDinghuofangshi(String dinghuofangshi) 
    {
        this.dinghuofangshi = dinghuofangshi;
    }

    public String getDinghuofangshi() 
    {
        return dinghuofangshi;
    }

    public void setJiesuanfangshi(String jiesuanfangshi) 
    {
        this.jiesuanfangshi = jiesuanfangshi;
    }

    public String getJiesuanfangshi() 
    {
        return jiesuanfangshi;
    }

    public void setWangshangjiesuan(String wangshangjiesuan) 
    {
        this.wangshangjiesuan = wangshangjiesuan;
    }

    public String getWangshangjiesuan() 
    {
        return wangshangjiesuan;
    }

    public void setChengxindengji(String chengxindengji) 
    {
        this.chengxindengji = chengxindengji;
    }

    public String getChengxindengji() 
    {
        return chengxindengji;
    }

    public void setDangweibianma(Long dangweibianma) 
    {
        this.dangweibianma = dangweibianma;
    }

    public Long getDangweibianma() 
    {
        return dangweibianma;
    }

    public void setDangwei(String dangwei) 
    {
        this.dangwei = dangwei;
    }

    public String getDangwei() 
    {
        return dangwei;
    }

    public void setRuwangriqi(Date ruwangriqi) 
    {
        this.ruwangriqi = ruwangriqi;
    }

    public Date getRuwangriqi() 
    {
        return ruwangriqi;
    }

    public void setZhongduancengji(String zhongduancengji) 
    {
        this.zhongduancengji = zhongduancengji;
    }

    public String getZhongduancengji() 
    {
        return zhongduancengji;
    }

    public void setZhongduanleibie(String zhongduanleibie) 
    {
        this.zhongduanleibie = zhongduanleibie;
    }

    public String getZhongduanleibie() 
    {
        return zhongduanleibie;
    }

    public void setZhongduanleibiexifen(String zhongduanleibiexifen) 
    {
        this.zhongduanleibiexifen = zhongduanleibiexifen;
    }

    public String getZhongduanleibiexifen() 
    {
        return zhongduanleibiexifen;
    }

    public void setXuejiadangwei(String xuejiadangwei) 
    {
        this.xuejiadangwei = xuejiadangwei;
    }

    public String getXuejiadangwei() 
    {
        return xuejiadangwei;
    }

    public void setXuejiayanzhongduanleixing(String xuejiayanzhongduanleixing) 
    {
        this.xuejiayanzhongduanleixing = xuejiayanzhongduanleixing;
    }

    public String getXuejiayanzhongduanleixing() 
    {
        return xuejiayanzhongduanleixing;
    }

    public void setCreatId(Long creatId) 
    {
        this.creatId = creatId;
    }

    public Long getCreatId() 
    {
        return creatId;
    }

    public void setCreatBy(String creatBy) 
    {
        this.creatBy = creatBy;
    }

    public String getCreatBy() 
    {
        return creatBy;
    }

    public void setPhoto(String photo) 
    {
        this.photo = photo;
    }

    public String getPhoto() 
    {
        return photo;
    }

    public void setCreatTime(Date creatTime) 
    {
        this.creatTime = creatTime;
    }

    public Date getCreatTime() 
    {
        return creatTime;
    }

    public void setOriginalLongitude(Double originalLongitude)
    {
        this.originalLongitude = originalLongitude;
    }

    public Double getOriginalLongitude()
    {
        return originalLongitude;
    }

    public void setOriginalLatitude(Double originalLatitude)
    {
        this.originalLatitude = originalLatitude;
    }

    public Double getOriginalLatitude()
    {
        return originalLatitude;
    }

    public void setLongitudeAfterOffset(Double longitudeAfterOffset)
    {
        this.longitudeAfterOffset = longitudeAfterOffset;
    }

    public Double getLongitudeAfterOffset()
    {
        return longitudeAfterOffset;
    }

    public void setLatitudeAfterOffset(Double latitudeAfterOffset)
    {
        this.latitudeAfterOffset = latitudeAfterOffset;
    }

    public Double getLatitudeAfterOffset()
    {
        return latitudeAfterOffset;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("provinces", getProvinces())
            .append("licence", getLicence())
            .append("merchantName", getMerchantName())
            .append("legalName", getLegalName())
            .append("merchantStatue", getMerchantStatue())
            .append("phoneNumber", getPhoneNumber())
            .append("county", getCounty())
            .append("countyId", getCountyId())
            .append("shichangbu", getShichangbu())
            .append("yingxiaoxian", getYingxiaoxian())
            .append("businessAddress", getBusinessAddress())
            .append("address", getAddress())
            .append("businessScope", getBusinessScope())
            .append("marketType", getMarketType())
            .append("marketTypeSegment", getMarketTypeSegment())
            .append("yetai", getYetai())
            .append("jingyingguimo", getJingyingguimo())
            .append("shangquan", getShangquan())
            .append("dinghuozhouqileixing", getDinghuozhouqileixing())
            .append("dinghuori", getDinghuori())
            .append("dinghuofangshi", getDinghuofangshi())
            .append("jiesuanfangshi", getJiesuanfangshi())
            .append("wangshangjiesuan", getWangshangjiesuan())
            .append("chengxindengji", getChengxindengji())
            .append("dangweibianma", getDangweibianma())
            .append("dangwei", getDangwei())
            .append("ruwangriqi", getRuwangriqi())
            .append("zhongduancengji", getZhongduancengji())
            .append("zhongduanleibie", getZhongduanleibie())
            .append("zhongduanleibiexifen", getZhongduanleibiexifen())
            .append("xuejiadangwei", getXuejiadangwei())
            .append("xuejiayanzhongduanleixing", getXuejiayanzhongduanleixing())
            .append("creatId", getCreatId())
            .append("creatBy", getCreatBy())
            .append("photo", getPhoto())
            .append("creatTime", getCreatTime())
            .append("updateId", getUpdateId())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Date getLastAssessedDate() {
        return lastAssessedDate;
    }

    public void setLastAssessedDate(Date lastAssessedDate) {
        this.lastAssessedDate = lastAssessedDate;
    }
}
