package com.hz.merchant.domain;

import java.util.List;

/**
 * 商户抽取响应DTO
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public class MerchantSelectionResponse {
    
    /** 抽取成功标志 */
    private boolean success;
    
    /** 抽取的商户列表 */
    private List<MerchantInfo> merchants;
    
    /** 总距离（仅智能抽取时有效） */
    private Double totalDistance;
    
    /** 抽取数量 */
    private Integer count;
    
    /** 消息 */
    private String message;
    
    /** 抽取统计信息 */
    private MerchantSelectionStatistics statistics;
    
    /** 配置中的总抽取数量 */
    private Integer totalCountConfig;
    
    public MerchantSelectionResponse() {
    }
    
    public MerchantSelectionResponse(boolean success, List<MerchantInfo> merchants, String message) {
        this.success = success;
        this.merchants = merchants;
        this.message = message;
        this.count = merchants != null ? merchants.size() : 0;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public List<MerchantInfo> getMerchants() {
        return merchants;
    }
    
    public void setMerchants(List<MerchantInfo> merchants) {
        this.merchants = merchants;
        this.count = merchants != null ? merchants.size() : 0;
    }
    
    public Double getTotalDistance() {
        return totalDistance;
    }
    
    public void setTotalDistance(Double totalDistance) {
        this.totalDistance = totalDistance;
    }
    
    public Integer getCount() {
        return count;
    }
    
    public void setCount(Integer count) {
        this.count = count;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public MerchantSelectionStatistics getStatistics() {
        return statistics;
    }
    
    public void setStatistics(MerchantSelectionStatistics statistics) {
        this.statistics = statistics;
    }
    
    public Integer getTotalCountConfig() {
        return totalCountConfig;
    }
    
    public void setTotalCountConfig(Integer totalCountConfig) {
        this.totalCountConfig = totalCountConfig;
    }
    
    /**
     * 商户抽取统计信息
     */
    public static class MerchantSelectionStatistics {
        private int totalCount;
        private java.util.Map<String, Long> chengxindengjiCounts;
        private java.util.Map<String, Long> zhongduancengjiCounts;
        private java.util.Map<String, Long> jingyingguimoCounts;
        private java.util.Map<String, Long> yetaiCounts;

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public java.util.Map<String, Long> getChengxindengjiCounts() {
            return chengxindengjiCounts;
        }

        public void setChengxindengjiCounts(java.util.Map<String, Long> chengxindengjiCounts) {
            this.chengxindengjiCounts = chengxindengjiCounts;
        }

        public java.util.Map<String, Long> getZhongduancengjiCounts() {
            return zhongduancengjiCounts;
        }

        public void setZhongduancengjiCounts(java.util.Map<String, Long> zhongduancengjiCounts) {
            this.zhongduancengjiCounts = zhongduancengjiCounts;
        }

        public java.util.Map<String, Long> getJingyingguimoCounts() {
            return jingyingguimoCounts;
        }

        public void setJingyingguimoCounts(java.util.Map<String, Long> jingyingguimoCounts) {
            this.jingyingguimoCounts = jingyingguimoCounts;
        }

        public java.util.Map<String, Long> getYetaiCounts() {
            return yetaiCounts;
        }

        public void setYetaiCounts(java.util.Map<String, Long> yetaiCounts) {
            this.yetaiCounts = yetaiCounts;
        }
    }
} 