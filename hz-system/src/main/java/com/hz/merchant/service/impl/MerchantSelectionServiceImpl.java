package com.hz.merchant.service.impl;

import com.hz.merchant.config.MerchantSelectionProperties;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.domain.MerchantSelectionRequest;
import com.hz.merchant.domain.MerchantSelectionResponse;
import com.hz.merchant.mapper.MerchantInfoMapper;
import com.hz.merchant.service.IMerchantSelectionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商户选择服务实现类 - 优化版本
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Service
public class MerchantSelectionServiceImpl implements IMerchantSelectionService {
    
    private static final Logger logger = LoggerFactory.getLogger(MerchantSelectionServiceImpl.class);
    
    @Autowired
    private MerchantInfoMapper merchantInfoMapper;
    
    @Autowired
    private MerchantSelectionProperties properties;
    
    @Override
    public MerchantSelectionResponse selectMerchantsForAssessment(Map<String, Object> params) {
        try {
            logger.info("开始智能抽取商户...");
            
            // 1. 获取符合条件的商户数据
            List<MerchantInfo> allMerchants = getAllMerchantsWithCoordinates(params);
            if (allMerchants.isEmpty()) {
                MerchantSelectionResponse response = new MerchantSelectionResponse(false, new ArrayList<>(), "没有找到符合条件的商户数据");
                response.setTotalCountConfig(properties.getTotalCount());
                return response;
            }
            
            // 2. 委托给算法选择器
            IntelligentMerchantSelector selector = new IntelligentMerchantSelector(allMerchants, properties);
            IntelligentMerchantSelector.Solution bestSolution = selector.findOptimalSolution();
            
            // 3. 处理结果
            if (bestSolution != null) {
                MerchantSelectionResponse response = new MerchantSelectionResponse(true, bestSolution.getMerchants(), "智能抽取成功");
                response.setTotalDistance(bestSolution.getTotalDistance());
                response.setStatistics(calculateStatistics(bestSolution.getMerchants()));
                response.setTotalCountConfig(properties.getTotalCount());
                return response;
            }
            
            MerchantSelectionResponse response = new MerchantSelectionResponse(false, new ArrayList<>(), "未能找到符合条件的" + properties.getTotalCount() + "个商户组合");
            response.setTotalCountConfig(properties.getTotalCount());
            return response;
            
        } catch (Exception e) {
            logger.error("抽取商户时发生异常", e);
            MerchantSelectionResponse response = new MerchantSelectionResponse(false, new ArrayList<>(), "抽取商户时发生异常: " + e.getMessage());
            response.setTotalCountConfig(properties.getTotalCount());
            return response;
        }
    }
    
    @Override
    public List<MerchantInfo> selectMerchantsByConditions(Long deptId, String chengxindengji, String zhongduancengji, String jingyingguimo, Integer num) {
        MerchantInfo query = new MerchantInfo();
        query.setCountyId(deptId);
        query.setChengxindengji(chengxindengji);
        query.setZhongduancengji(zhongduancengji);
        query.setJingyingguimo(jingyingguimo);
        if (num != null) {
            query.getParams().put("limit", num);
        }
        return merchantInfoMapper.selectMerchantInfoList(query);
    }
    
    private MerchantSelectionResponse.MerchantSelectionStatistics calculateStatistics(List<MerchantInfo> merchants) {
        MerchantSelectionResponse.MerchantSelectionStatistics stats = new MerchantSelectionResponse.MerchantSelectionStatistics();
        if (merchants == null || merchants.isEmpty()) {
            return stats;
        }
        
        stats.setTotalCount(merchants.size());
        stats.setChengxindengjiCounts(merchants.stream().collect(Collectors.groupingBy(MerchantInfo::getChengxindengji, Collectors.counting())));
        stats.setZhongduancengjiCounts(merchants.stream().collect(Collectors.groupingBy(MerchantInfo::getZhongduancengji, Collectors.counting())));
        stats.setJingyingguimoCounts(merchants.stream().collect(Collectors.groupingBy(MerchantInfo::getJingyingguimo, Collectors.counting())));
        stats.setYetaiCounts(merchants.stream().collect(Collectors.groupingBy(MerchantInfo::getYetai, Collectors.counting())));
        
        return stats;
    }
    
    private List<MerchantInfo> getAllMerchantsWithCoordinates(Map<String, Object> params) {
        logger.debug("从数据库获取所有商户坐标信息，参数: {}", params);
        MerchantInfo query = new MerchantInfo();
        if (params != null) {
            if (params.get("deptId") != null) query.setCountyId((Long) params.get("deptId"));
            if (params.get("chengxindengji") != null) query.setChengxindengji((String) params.get("chengxindengji"));
            if (params.get("zhongduancengji") != null) query.setZhongduancengji((String) params.get("zhongduancengji"));
            if (params.get("jingyingguimo") != null) query.setJingyingguimo((String) params.get("jingyingguimo"));
        }
        List<MerchantInfo> merchants = merchantInfoMapper.selectMerchantInfoList(query);
        return merchants.stream()
                .filter(this::hasValidCoordinates)
                .collect(Collectors.toList());
    }

    private boolean hasValidCoordinates(MerchantInfo merchant) {
        return isValidCoordinate(merchant.getOriginalLongitude()) && isValidCoordinate(merchant.getOriginalLatitude());
    }
    
    private boolean isValidCoordinate(Double coordinate) {
        if (coordinate == null) {
            return false;
        }
        try {
            return coordinate != 0.0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    @Override
    public MerchantSelectionResponse selectMerchants(MerchantSelectionRequest request) {
        if (isConditionalSelection(request)) {
            // 条件抽取
            return performConditionalSelection(request);
        } else {
            // 智能抽取
            return performIntelligentSelection(request);
        }
    }
    
    private boolean isConditionalSelection(MerchantSelectionRequest request) {
        return request.getNum() != null && request.getNum() > 0;
    }
    
    private MerchantSelectionResponse performConditionalSelection(MerchantSelectionRequest request) {
        List<MerchantInfo> selectedMerchants = selectMerchantsByConditions(
                request.getDeptId(),
                request.getChengxindengji(),
                request.getZhongduancengji(),
                request.getJingyingguimo(),
                request.getNum()
        );
        
        if (selectedMerchants != null && !selectedMerchants.isEmpty()) {
            MerchantSelectionResponse response = new MerchantSelectionResponse(true, selectedMerchants, "条件抽取成功，共抽取到" + selectedMerchants.size() + "个商户");
            response.setStatistics(calculateStatistics(selectedMerchants));
            response.setTotalCountConfig(properties.getTotalCount());
            return response;
        } else {
            MerchantSelectionResponse response = new MerchantSelectionResponse(false, List.of(), "未找到符合条件的商户");
            response.setTotalCountConfig(properties.getTotalCount());
            return response;
        }
    }
    
    private MerchantSelectionResponse performIntelligentSelection(MerchantSelectionRequest request) {
        Map<String, Object> params = buildSelectionParams(request);
        return selectMerchantsForAssessment(params);
    }
    
    private Map<String, Object> buildSelectionParams(MerchantSelectionRequest request) {
        Map<String, Object> params = new HashMap<>();
        if (request.getDeptId() != null) {
            params.put("deptId", request.getDeptId());
        }
        if (request.getChengxindengji() != null) {
            params.put("chengxindengji", request.getChengxindengji());
        }
        if (request.getZhongduancengji() != null) {
            params.put("zhongduancengji", request.getZhongduancengji());
        }
        if (request.getJingyingguimo() != null) {
            params.put("jingyingguimo", request.getJingyingguimo());
        }
        return params;
    }
} 