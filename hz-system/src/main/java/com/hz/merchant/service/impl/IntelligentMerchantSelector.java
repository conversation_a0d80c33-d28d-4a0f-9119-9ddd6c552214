package com.hz.merchant.service.impl;

import com.hz.merchant.config.MerchantSelectionProperties;
import com.hz.merchant.domain.MerchantInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能商户选择器 - 包含优化后的算法
 */
public class IntelligentMerchantSelector {

    private static final Logger logger = LoggerFactory.getLogger(IntelligentMerchantSelector.class);

    private final MerchantSelectionProperties properties;
    private final Map<MerchantKey, List<MerchantInfo>> groups;
    private final Random random;
    private final Map<MerchantKey, Integer> quotas;
    private final List<MerchantInfo> allMerchants;
    private final Map<String, Double> distanceCache = new HashMap<>();

    public IntelligentMerchantSelector(List<MerchantInfo> allMerchants, MerchantSelectionProperties properties) {
        this.properties = properties;
        this.allMerchants = allMerchants;
        this.random = new Random();
        this.groups = classifyMerchants(allMerchants);
        this.quotas = generateSmartQuota();
    }

    /**
     * 寻找最优解决方案
     */
    public Solution findOptimalSolution() {
        Solution bestSolution = null;
        for (int i = 0; i < properties.getSimulationIterations(); i++) {
            Solution currentSolution = buildSolutionWithQuota(this.quotas);
            if (bestSolution == null || (currentSolution != null && currentSolution.getTotalDistance() < bestSolution.getTotalDistance())) {
                bestSolution = currentSolution;
            }
        }
        return bestSolution;
    }

    private Solution buildSolutionWithQuota(Map<MerchantKey, Integer> targetQuota) {
        List<MerchantInfo> selectedMerchants = new ArrayList<>();
        for (Map.Entry<MerchantKey, Integer> entry : targetQuota.entrySet()) {
            MerchantKey key = entry.getKey();
            Integer count = entry.getValue();
            List<MerchantInfo> group = groups.get(key);
            if (group != null && !group.isEmpty()) {
                List<MerchantInfo> shuffled = new ArrayList<>(group);
                Collections.shuffle(shuffled, random);
                selectedMerchants.addAll(shuffled.subList(0, Math.min(count, shuffled.size())));
            }
        }

        if (selectedMerchants.size() != properties.getTotalCount()) {
             return null; //无法满足配额
        }

        double totalDistance = calculateTotalDistance(selectedMerchants);
        return new Solution(selectedMerchants, totalDistance);
    }

    private Map<MerchantKey, Integer> generateSmartQuota() {
        Map<MerchantKey, Integer> finalQuotas = new HashMap<>();

        final String URBAN_TYPE = "城镇商户";
        final String RURAL_TYPE = "农村商户";

        long countUA = getGroupSize(new MerchantKey(URBAN_TYPE, true));
        long countUN = getGroupSize(new MerchantKey(URBAN_TYPE, false));
        long countRA = getGroupSize(new MerchantKey(RURAL_TYPE, true));
        long countRN = getGroupSize(new MerchantKey(RURAL_TYPE, false));

        // 动态计算目标配额，基于总目标数量
        int totalTarget = properties.getTotalCount();
        double urbanRatio = 0.6;  // 城镇商户占60%
        double ruralRatio = 0.4;  // 农村商户占40%
        double affiliateRatio = 0.2;  // 加盟商户占20%

        int totalUrbanTarget = (int) Math.round(totalTarget * urbanRatio);
        int totalRuralTarget = totalTarget - totalUrbanTarget;  // 确保总和正确
        int totalAffiliateTarget = (int) Math.round(totalTarget * affiliateRatio);

        // 初始化所有配额为0
        int quotaUA = 0; // Urban Affiliate
        int quotaUN = 0; // Urban Non-Affiliate
        int quotaRA = 0; // Rural Affiliate
        int quotaRN = 0; // Rural Non-Affiliate

        // 检查是否有足够的商户类型
        long totalAvailableAffiliates = countUA + countRA;
        long totalAvailableNonAffiliates = countUN + countRN;
        long totalAvailableUrban = countUA + countUN;
        long totalAvailableRural = countRA + countRN;

        // 如果某些类型的商户数量不足，需要调整策略
        if (totalAvailableAffiliates < totalAffiliateTarget) {
            totalAffiliateTarget = (int) totalAvailableAffiliates;
        }

        if (totalAvailableUrban < totalUrbanTarget) {
            totalUrbanTarget = (int) totalAvailableUrban;
            totalRuralTarget = totalTarget - totalUrbanTarget;
        }

        if (totalAvailableRural < totalRuralTarget) {
            totalRuralTarget = (int) totalAvailableRural;
            totalUrbanTarget = totalTarget - totalRuralTarget;
        }

        // 第一步：分配加盟商户配额
        if (totalAvailableAffiliates > 0 && totalAffiliateTarget > 0) {
            // 按可用数量比例分配加盟商户配额
            double exactUA = totalAvailableAffiliates > 0 ? 
                (double) countUA / totalAvailableAffiliates * totalAffiliateTarget : 0;
            double exactRA = totalAvailableAffiliates > 0 ? 
                (double) countRA / totalAvailableAffiliates * totalAffiliateTarget : 0;
            
            quotaUA = (int) Math.round(exactUA);
            quotaRA = (int) Math.round(exactRA);

            // 确保不超过实际可用数量
            quotaUA = Math.min(quotaUA, (int) countUA);
            quotaRA = Math.min(quotaRA, (int) countRA);

            // 处理舍入误差
            int actualAffiliateTotal = quotaUA + quotaRA;
            int difference = totalAffiliateTarget - actualAffiliateTotal;
            
            if (difference > 0 && countUA > quotaUA) {
                int canAddUA = Math.min(difference, (int) countUA - quotaUA);
                quotaUA += canAddUA;
                difference -= canAddUA;
            }
            if (difference > 0 && countRA > quotaRA) {
                int canAddRA = Math.min(difference, (int) countRA - quotaRA);
                quotaRA += canAddRA;
            }
        }

        // 第二步：分配非加盟商户配额
        int remainingUrban = totalUrbanTarget - quotaUA;
        int remainingRural = totalRuralTarget - quotaRA;
        
        quotaUN = Math.min(remainingUrban, (int) countUN);
        quotaRN = Math.min(remainingRural, (int) countRN);

        // 第三步：处理配额不足的情况，进行灵活调整
        int currentTotal = quotaUA + quotaUN + quotaRA + quotaRN;
        int shortage = totalTarget - currentTotal;

        if (shortage > 0) {
            // 优先从可用商户数较多的分组补充，但保持一定的平衡性
            List<QuotaAdjustment> adjustments = Arrays.asList(
                new QuotaAdjustment("城镇加盟", quotaUA, (int)countUA),
                new QuotaAdjustment("城镇非加盟", quotaUN, (int)countUN),
                new QuotaAdjustment("农村加盟", quotaRA, (int)countRA),
                new QuotaAdjustment("农村非加盟", quotaRN, (int)countRN)
            );
            
            // 按可扩展空间排序，优先使用空间大的分组
            adjustments.sort((a, b) -> Integer.compare(b.getExpandableSpace(), a.getExpandableSpace()));
            
            for (QuotaAdjustment adj : adjustments) {
                if (shortage <= 0) break;
                int canAdd = Math.min(shortage, adj.getExpandableSpace());
                if (canAdd > 0) {
                    switch (adj.getType()) {
                        case "城镇非加盟": quotaUN += canAdd; break;
                        case "农村非加盟": quotaRN += canAdd; break;
                        case "城镇加盟": quotaUA += canAdd; break;
                        case "农村加盟": quotaRA += canAdd; break;
                    }
                    shortage -= canAdd;
                }
            }
        } else if (shortage < 0) {
            // 如果超出目标，需要减少配额
            // 从配额最多的非关键分组减少
            while (shortage < 0) {
                if (quotaUA > 1 && quotaUA > quotaRA && quotaUA > quotaUN && quotaUA > quotaRN) {
                    quotaUA--;
                } else if (quotaUN > 0) {
                    quotaUN--;
                } else if (quotaRN > 0) {
                    quotaRN--;
                } else if (quotaRA > 0) {
                    quotaRA--;
                } else {
                    break;
                }
                shortage++;
            }
        }

        // 设置最终配额
        finalQuotas.put(new MerchantKey(URBAN_TYPE, true), quotaUA);
        finalQuotas.put(new MerchantKey(URBAN_TYPE, false), quotaUN);
        finalQuotas.put(new MerchantKey(RURAL_TYPE, true), quotaRA);
        finalQuotas.put(new MerchantKey(RURAL_TYPE, false), quotaRN);
        
        return finalQuotas;
    }

    private static class QuotaAdjustment {
        private final String type;
        private final int currentQuota;
        private final int maxAvailable;

        public QuotaAdjustment(String type, int currentQuota, int maxAvailable) {
            this.type = type;
            this.currentQuota = currentQuota;
            this.maxAvailable = maxAvailable;
        }

        public String getType() { return type; }
        public int getExpandableSpace() { return Math.max(0, maxAvailable - currentQuota); }
    }
    
    private long getGroupSize(MerchantKey key) {
        return groups.getOrDefault(key, Collections.emptyList()).size();
    }

    private Map<MerchantKey, List<MerchantInfo>> classifyMerchants(List<MerchantInfo> merchants) {
        return merchants.stream()
                .filter(m -> m.getOriginalLongitude() != null && m.getOriginalLatitude() != null)
                .collect(Collectors.groupingBy(this::getMerchantKey));
    }

    private MerchantKey getMerchantKey(MerchantInfo merchant) {
        String marketType = merchant.getMarketType();
        String zhongduancengji = merchant.getZhongduancengji();
        
        // 根据market_type判断城镇/乡村
        String type;
        if (marketType != null && marketType.contains("乡村")) {
            type = "农村商户";
        } else {
            type = "城镇商户";  // 默认为城镇，包括城镇及其他类型
        }
        
        // 根据zhongduancengji判断加盟/非加盟
        // 普通终端、现代终端为非加盟，加盟店为加盟
        boolean isAffiliate = false;
        if (zhongduancengji != null) {
            if (zhongduancengji.contains("加盟店") || zhongduancengji.contains("加盟")) {
                isAffiliate = true;
            }
            // 普通终端、现代终端默认为非加盟 (isAffiliate = false)
        }
        
        return new MerchantKey(type, isAffiliate);
    }

    private double calculateTotalDistance(List<MerchantInfo> merchants) {
        if (merchants == null || merchants.size() < 2) return 0.0;
        
        List<MerchantInfo> tour = new ArrayList<>();
        List<MerchantInfo> unvisited = new ArrayList<>(merchants);
        MerchantInfo current = unvisited.remove(random.nextInt(unvisited.size()));
        tour.add(current);
        
        double totalDistance = 0.0;

        while(!unvisited.isEmpty()){
            MerchantInfo nearest = null;
            double nearestDistance = Double.MAX_VALUE;

            for(MerchantInfo candidate : unvisited){
                double distance = getDistance(current, candidate);
                if(distance < nearestDistance){
                    nearestDistance = distance;
                    nearest = candidate;
                }
            }
            totalDistance += nearestDistance;
            current = nearest;
            tour.add(current);
            unvisited.remove(current);
        }

        return totalDistance;
    }

    private double getDistance(MerchantInfo m1, MerchantInfo m2) {
        String key = m1.getId() < m2.getId() ? m1.getId() + "-" + m2.getId() : m2.getId() + "-" + m1.getId();
        return distanceCache.computeIfAbsent(key, k -> {
            try {
                double lon1 = m1.getOriginalLongitude();
                double lat1 = m1.getOriginalLatitude();
                double lon2 = m2.getOriginalLongitude();
                double lat2 = m2.getOriginalLatitude();
                return haversine(lat1, lon1, lat2, lon2);
            } catch (NumberFormatException e) {
                return Double.MAX_VALUE;
            }
        });
    }

    private double haversine(double lat1, double lon1, double lat2, double lon2) {
        double R = 6371; // Earth radius in kilometers
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }

    public static class Solution {
        private final List<MerchantInfo> merchants;
        private final double totalDistance;

        public Solution(List<MerchantInfo> merchants, double totalDistance) {
            this.merchants = new ArrayList<>(merchants);
            this.totalDistance = totalDistance;
        }

        public List<MerchantInfo> getMerchants() { return merchants; }
        public double getTotalDistance() { return totalDistance; }
    }

    static class MerchantKey {
        private final String type;
        private final boolean isAffiliate;

        public MerchantKey(String type, boolean isAffiliate) {
            this.type = type;
            this.isAffiliate = isAffiliate;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            MerchantKey that = (MerchantKey) obj;
            return isAffiliate == that.isAffiliate && Objects.equals(type, that.type);
        }

        @Override
        public int hashCode() {
            return Objects.hash(type, isAffiliate);
        }

        @Override
        public String toString() {
            return "(" + type + ", " + (isAffiliate ? "加盟" : "非加盟") + ")";
        }
    }
}