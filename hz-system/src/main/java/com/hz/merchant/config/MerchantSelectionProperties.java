package com.hz.merchant.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "hz.merchant-selection")
public class MerchantSelectionProperties {

    private int totalCount;
    private int simulationIterations;
    private double scoreWeightDistance;
    private double scoreWeightTime;
    private Quota quota = new Quota();

    public static class Quota {
        private int ruralNonAffiliateFixed;
        private int ruralAffiliateRandomBound;
        private int urbanTotalRandomBound;
        private int urbanAffiliateRandomBound;

        // Getters and Setters for Quota
        public int getRuralNonAffiliateFixed() {
            return ruralNonAffiliateFixed;
        }

        public void setRuralNonAffiliateFixed(int ruralNonAffiliateFixed) {
            this.ruralNonAffiliateFixed = ruralNonAffiliateFixed;
        }

        public int getRuralAffiliateRandomBound() {
            return ruralAffiliateRandomBound;
        }

        public void setRuralAffiliateRandomBound(int ruralAffiliateRandomBound) {
            this.ruralAffiliateRandomBound = ruralAffiliateRandomBound;
        }

        public int getUrbanTotalRandomBound() {
            return urbanTotalRandomBound;
        }

        public void setUrbanTotalRandomBound(int urbanTotalRandomBound) {
            this.urbanTotalRandomBound = urbanTotalRandomBound;
        }

        public int getUrbanAffiliateRandomBound() {
            return urbanAffiliateRandomBound;
        }

        public void setUrbanAffiliateRandomBound(int urbanAffiliateRandomBound) {
            this.urbanAffiliateRandomBound = urbanAffiliateRandomBound;
        }
    }

    // Getters and Setters for MerchantSelectionProperties
    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getSimulationIterations() {
        return simulationIterations;
    }

    public void setSimulationIterations(int simulationIterations) {
        this.simulationIterations = simulationIterations;
    }

    public double getScoreWeightDistance() {
        return scoreWeightDistance;
    }

    public void setScoreWeightDistance(double scoreWeightDistance) {
        this.scoreWeightDistance = scoreWeightDistance;
    }

    public double getScoreWeightTime() {
        return scoreWeightTime;
    }

    public void setScoreWeightTime(double scoreWeightTime) {
        this.scoreWeightTime = scoreWeightTime;
    }

    public Quota getQuota() {
        return quota;
    }

    public void setQuota(Quota quota) {
        this.quota = quota;
    }
} 