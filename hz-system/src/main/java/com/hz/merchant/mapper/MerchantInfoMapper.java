package com.hz.merchant.mapper;

import com.hz.merchant.domain.MerchantInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface MerchantInfoMapper{
    /**
     * 查询商户信息
     *
     * @param id 商户信息主键
     * @return 商户信息
     */
    public MerchantInfo selectMerchantInfoById(Long id);

    /**
     * 根据许可证号查询商户信息
     *
     * @param licence 许可证号
     * @return 商户信息
     */
    public MerchantInfo selectMerchantInfoByLicence(String licence);

    /**
     * 根据ID批量查询商户信息
     *
     * @param ids ID数组
     * @return 商户信息列表
     */
    public List<MerchantInfo> selectMerchantInfoByIds(Long[] ids);

    /**
     * 查询商户信息列表
     *
     * @param merchantInfo 商户信息
     * @return 商户信息集合
     */
    public List<MerchantInfo> selectMerchantInfoList(MerchantInfo merchantInfo);

    /**
     * 新增商户信息
     *
     * @param merchantInfo 商户信息
     * @return 结果
     */
    public int insertMerchantInfo(MerchantInfo merchantInfo);

    /**
     * 修改商户信息
     *
     * @param merchantInfo 商户信息
     * @return 结果
     */
    public int updateMerchantInfo(MerchantInfo merchantInfo);

    /**
     * 删除商户信息
     *
     * @param id 商户信息主键
     * @return 结果
     */
    public int deleteMerchantInfoById(Long id);

    /**
     * 批量删除商户信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMerchantInfoByIds(Long[] ids);

    /**
     * 修改商户状态
     *
     * @param merchantInfo 商户信息
     * @return 结果
     */
    public int updateMerchantStatus(MerchantInfo merchantInfo);

    /**
     * 数据采集：商户经纬度
     *
     * @param classify 分类（价格、库存）
     * @param stId 调研任务id
     * @return
     */
    List<MerchantInfo> selectMerchantLocationListBySjcj(@Param("classify") String classify, @Param("stId") Long stId);

}
