package com.hz.quarterlyassessments.domain;

import java.util.Date;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hz.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

/**
 * 月度考核对象 monthly_assessments
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public class MonthlyAssessments extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 季度+月份 */
    private String id;

    /** $column.columnComment */
    private String mId;

    /** 县区id*/
    @Excel(name = "县区id")
    private String deptId;

    /** 所属季度 */
    @Excel(name = "所属季度")
    private String quarterlyAssessmentId;

    /** 月度考核名称 */
    @Excel(name = "月度考核名称")
    private String name;

    /** 所属月份 */
    @Excel(name = "所属月份")
    private Long month;

    /** 考核类型 */
    @Excel(name = "考核类型")
    private String assessmentType;

    /** 考核县区 */
    @Excel(name = "考核县区")
    private String district;

    /** 检查户数 */
    @Excel(name = "检查户数")
    private Long inspectedHouseholds;

    /** 状态 1-未开始 2-进行中 3-需签字 4-已完成 */
    @Excel(name = "状态") 
    private String status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 月度考核人员列表 */
    private List<MonthlyAssessmentStaff> staffList;

    /** 人员信息键值对 (roleName -> userName) */
    private Map<String, String> staffMap;

    /** 状态文本描述 */
    private String statusText;

    /** JSON数据 */
    @Excel(name = "路径点")
    private String jsonData;



    /** 所属年份（从季度考核表关联查询） */
    @Excel(name = "所属年份")
    private String year;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setQuarterlyAssessmentId(String quarterlyAssessmentId) 
    {
        this.quarterlyAssessmentId = quarterlyAssessmentId;
    }

    public String getQuarterlyAssessmentId() 
    {
        return quarterlyAssessmentId;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setMonth(Long month) 
    {
        this.month = month;
    }

    public Long getMonth() 
    {
        return month;
    }
    public void setAssessmentType(String assessmentType) 
    {
        this.assessmentType = assessmentType;
    }

    public String getAssessmentType() 
    {
        return assessmentType;
    }
    public void setDistrict(String district) 
    {
        this.district = district;
    }

    public String getDistrict() 
    {
        return district;
    }
    public void setInspectedHouseholds(Long inspectedHouseholds) 
    {
        this.inspectedHouseholds = inspectedHouseholds;
    }

    public Long getInspectedHouseholds() 
    {
        return inspectedHouseholds;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    public void setStaffList(List<MonthlyAssessmentStaff> staffList) 
    {
        this.staffList = staffList;
    }

    public List<MonthlyAssessmentStaff> getStaffList() 
    {
        return staffList;
    }

    public void setStaffMap(Map<String, String> staffMap) 
    {
        this.staffMap = staffMap;
    }

    public Map<String, String> getStaffMap() 
    {
        return staffMap;
    }

    public String getStatusText()
    {
        return getStatusTextByCode(this.status);
    }

    public void setStatusText(String statusText)
    {
        this.statusText = statusText;
    }

    public String getJsonData()
    {
        return jsonData;
    }

    public void setJsonData(String jsonData)
    {
        this.jsonData = jsonData;
    }



    public String getYear()
    {
        return year;
    }

    public void setYear(String year)
    {
        this.year = year;
    }

    /**
     * 根据状态码获取状态文本描述
     * @param statusCode 状态码
     * @return 状态文本描述
     */
    private String getStatusTextByCode(String statusCode)
    {
        if (statusCode == null) {
            return "未知状态";
        }
        switch (statusCode) {
            case "1":
                return "未开始";
            case "2":
                return "进行中";
            case "3":
                return "需签字";
            case "4":
                return "已完成";
            default:
                return "未知状态";
        }
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("quarterlyAssessmentId", getQuarterlyAssessmentId())
            .append("name", getName())
            .append("month", getMonth())
            .append("assessmentType", getAssessmentType())
            .append("district", getDistrict())
            .append("inspectedHouseholds", getInspectedHouseholds())
            .append("status", getStatus())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .append("staffList", getStaffList())
            .append("jsonData", getJsonData())
            .toString();
    }

    public String getmId() {
        return mId;
    }

    public void setmId(String mId) {
        this.mId = mId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }
}
