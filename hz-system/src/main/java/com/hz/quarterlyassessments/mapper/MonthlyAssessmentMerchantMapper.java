package com.hz.quarterlyassessments.mapper;

import java.util.Date;
import java.util.List;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentMerchant;
import com.hz.merchant.domain.MerchantInfo;
import org.apache.ibatis.annotations.Param;
import com.hz.quarterlyassessments.domain.dto.MonthlyMerchantAssessmentDto;

/**
 * 月度考核商户关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface MonthlyAssessmentMerchantMapper 
{
    /**
     * 查询月度考核商户关联
     * 
     * @param id 月度考核商户关联主键
     * @return 月度考核商户关联
     */
    public MonthlyAssessmentMerchant selectMonthlyAssessmentMerchantById(@Param("id") Long id, @Param("type") String type);

    /**
     * 查询月度考核商户关联列表
     * 
     * @param monthlyAssessmentMerchant 月度考核商户关联
     * @return 月度考核商户关联集合
     */
    public List<MonthlyAssessmentMerchant> selectMonthlyAssessmentMerchantList(MonthlyAssessmentMerchant monthlyAssessmentMerchant);

    /**
     * 根据月度考核ID查询关联的商户列表（包含商户详细信息）
     *
     * @param monthlyAssessmentId 月度考核ID
     * @return 月度考核商户关联集合
     */
    public List<MonthlyAssessmentMerchant> selectMerchantsByMonthlyAssessmentId(@Param("monthlyAssessmentId") String monthlyAssessmentId, @Param("selectionType") String selectionType);

    /**
     * 新增月度考核商户关联
     * 
     * @param monthlyAssessmentMerchant 月度考核商户关联
     * @return 结果
     */
    public int insertMonthlyAssessmentMerchant(MonthlyAssessmentMerchant monthlyAssessmentMerchant);

    /**
     * 批量新增月度考核商户关联
     * 
     * @param monthlyAssessmentMerchants 月度考核商户关联列表
     * @return 结果
     */
    public int batchInsertMonthlyAssessmentMerchants(List<MonthlyAssessmentMerchant> monthlyAssessmentMerchants);

    /**
     * 修改月度考核商户关联
     * 
     * @param monthlyAssessmentMerchant 月度考核商户关联
     * @return 结果
     */
    public int updateMonthlyAssessmentMerchant(MonthlyAssessmentMerchant monthlyAssessmentMerchant);

    /**
     * 删除月度考核商户关联
     * 
     * @param id 月度考核商户关联主键
     * @return 结果
     */
    public int deleteMonthlyAssessmentMerchantById(Long id);

    /**
     * 批量删除月度考核商户关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonthlyAssessmentMerchantByIds(Long[] ids);

    /**
     * 根据月度考核ID删除商户关联
     *
     * @param monthlyAssessmentId 月度考核ID
     * @return 结果
     */
    public int deleteByMonthlyAssessmentId(@Param("monthlyAssessmentId") String monthlyAssessmentId);

    /**
     * 根据季度考核ID批量删除商户关联
     * 
     * @param quarterlyAssessmentId 季度考核ID
     * @return 结果
     */
    public int deleteByQuarterlyAssessmentId(@Param("quarterlyAssessmentId") String quarterlyAssessmentId);

    /**
     * 统计月度考核关联的商户数量
     *
     * @param monthlyAssessmentId 月度考核ID
     * @return 商户数量
     */
    public int countMerchantsByMonthlyAssessmentId(@Param("monthlyAssessmentId") String monthlyAssessmentId);

    /**
     * 检查商户是否已被关联到指定月度考核
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param merchantId 商户ID
     * @return 关联记录数量
     */
    public int checkMerchantAssociation(@Param("monthlyAssessmentId") String monthlyAssessmentId,
                                       @Param("merchantId") Long merchantId);



    /**
     * 更新商户闭店状态
     * 
     * @param id 月度考核商户关联ID
     * @param assessmentStatus 闭店状态：0-正常营业，1-闭店
     * @return 结果
     */
    public int updateAssessmentStatus(@Param("id") Long id, @Param("assessmentStatus") String assessmentStatus);



    /**
     * 根据月度考核ID查询商户考核数据（精简字段）
     *
     * @param monthlyAssessmentId 月度考核ID
     * @return 商户考核数据列表（精简字段）
     */
    public List<MonthlyMerchantAssessmentDto> selectMerchantAssessmentsSummaryByMonthlyId(@Param("monthlyAssessmentId") String monthlyAssessmentId);



    /**
     * 更新专卖签到签退状态
     * 
     * @param id 月度考核商户关联ID
     * @param zhuanmaiCheckStatus 专卖状态值：0-未签到，1-已签到，2-已提交表单，3-已签退
     * @param zhuanmaiCheckInTime 专卖签到时间（可选）
     * @param zhuanmaiCheckOutTime 专卖签退时间（可选）
     * @param updateId 更新人ID
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 结果
     */
    public int updateZhuanmaiCheckStatus(@Param("id") Long id,
                                        @Param("zhuanmaiCheckStatus") String zhuanmaiCheckStatus,
                                        @Param("zhuanmaiCheckInTime") Date zhuanmaiCheckInTime,
                                        @Param("zhuanmaiCheckOutTime") Date zhuanmaiCheckOutTime,
                                        @Param("updateId") Long updateId,
                                        @Param("updateBy") String updateBy,
                                        @Param("updateTime") Date updateTime);

    /**
     * 更新营销签到签退状态
     * 
     * @param id 月度考核商户关联ID
     * @param yingxiaoCheckStatus 营销状态值：0-未签到，1-已签到，2-已提交表单，3-已签退
     * @param yingxiaoCheckInTime 营销签到时间（可选）
     * @param yingxiaoCheckOutTime 营销签退时间（可选）
     * @param updateId 更新人ID
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 结果
     */
    public int updateYingxiaoCheckStatus(@Param("id") Long id,
                                        @Param("yingxiaoCheckStatus") String yingxiaoCheckStatus,
                                        @Param("yingxiaoCheckInTime") Date yingxiaoCheckInTime,
                                        @Param("yingxiaoCheckOutTime") Date yingxiaoCheckOutTime,
                                        @Param("updateId") Long updateId,
                                        @Param("updateBy") String updateBy,
                                        @Param("updateTime") Date updateTime);



    /**
     * 根据月度考核ID统计专卖和营销都签退（状态都为3）的商户数量（排除无证户和闭店商户）
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 专卖和营销都签退的商户数量（不包含selectionType为2和assessmentStatus为1的商户）
     */
    public int countBothCheckStatusThreeByMonthlyAssessmentId(@Param("monthlyAssessmentId") Long monthlyAssessmentId);
} 