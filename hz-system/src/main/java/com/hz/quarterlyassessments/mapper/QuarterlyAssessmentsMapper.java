package com.hz.quarterlyassessments.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.hz.quarterlyassessments.domain.QuarterlyAssessments;
import com.hz.quarterlyassessments.domain.MonthlyAssessments;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentStaff;

/**
 * 季度考核Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface QuarterlyAssessmentsMapper 
{
    /**
     * 查询季度考核
     * 
     * @param id 季度考核主键
     * @return 季度考核
     */
    public QuarterlyAssessments selectQuarterlyAssessmentsById(String id);

    /**
     * 查询季度考核列表
     * 
     * @param quarterlyAssessments 季度考核
     * @return 季度考核集合
     */
    public List<QuarterlyAssessments> selectQuarterlyAssessmentsList(QuarterlyAssessments quarterlyAssessments);

    /**
     * 新增季度考核
     * 
     * @param quarterlyAssessments 季度考核
     * @return 结果
     */
    public int insertQuarterlyAssessments(QuarterlyAssessments quarterlyAssessments);

    /**
     * 修改季度考核
     * 
     * @param quarterlyAssessments 季度考核
     * @return 结果
     */
    public int updateQuarterlyAssessments(QuarterlyAssessments quarterlyAssessments);

    /**
     * 删除季度考核
     * 
     * @param id 季度考核主键
     * @return 结果
     */
    public int deleteQuarterlyAssessmentsById(String id);

    /**
     * 批量删除季度考核
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQuarterlyAssessmentsByIds(String[] ids);

    /**
     * 批量删除月度考核
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonthlyAssessmentsByQuarterlyAssessmentIds(String[] ids);
    
    /**
     * 批量新增月度考核
     * 
     * @param monthlyAssessmentsList 月度考核列表
     * @return 结果
     */
    public int batchMonthlyAssessments(List<MonthlyAssessments> monthlyAssessmentsList);

    /**
     * 根据季度考核ID查询月度考核列表
     * 
     * @param quarterlyAssessmentId 季度考核ID
     * @return 月度考核列表
     */
    public List<MonthlyAssessments> selectMonthlyAssessmentsByQuarterlyId(String quarterlyAssessmentId);
    

    /**
     * 通过季度考核主键删除月度考核信息
     * 
     * @param id 季度考核ID
     * @return 结果
     */
    public int deleteMonthlyAssessmentsByQuarterlyAssessmentId(String id);

    /**
     * 批量新增月度考核人员
     * 
     * @param staffList 月度考核人员列表
     * @return 结果
     */
    public int batchMonthlyAssessmentStaff(List<MonthlyAssessmentStaff> staffList);

    /**
     * 通过月度考核ID删除人员信息
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 结果
     */
    public int deleteMonthlyAssessmentStaffByMonthlyAssessmentId(String monthlyAssessmentId);

    /**
     * 通过季度考核ID批量删除月度考核人员信息
     * 
     * @param ids 季度考核ID数组
     * @return 结果
     */
    public int deleteMonthlyAssessmentStaffByQuarterlyAssessmentIds(String[] ids);

    /**
     * 检查指定年份和季度是否已存在季度考核
     * 
     * @param year 年份
     * @param quarter 季度
     * @return 季度考核对象，如果不存在则返回null
     */
    public QuarterlyAssessments checkYearQuarterExists(String year, Long quarter);

    /**
     * 根据月度考核ID查询月度考核详情（包括人员配置）
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 月度考核详情
     */
    public MonthlyAssessments selectMonthlyAssessmentWithStaffById(Long monthlyAssessmentId);
    
    /**
     * 修改月度考核
     * 
     * @param monthlyAssessments 月度考核
     * @return 结果
     */
    public int updateMonthlyAssessments(MonthlyAssessments monthlyAssessments);

    public int release(@Param("monthlyId") Long monthlyId, @Param("status") String status);

    /**
     * 分页查询所有月度考核详细信息（包括人员配置）
     * 
     * @param monthlyAssessments 查询条件
     * @return 月度考核详细信息列表
     */
    public List<MonthlyAssessments> selectMonthlyAssessmentsWithStaffList(MonthlyAssessments monthlyAssessments);

    /**
     * 分页查询指定用户参与的月度考核详细信息（包括人员配置）
     * 
     * @param monthlyAssessments 查询条件
     * @param userName 用户名
     * @return 月度考核详细信息列表
     */
    public List<MonthlyAssessments> selectMonthlyAssessmentsWithStaffListByUser(@Param("monthlyAssessments") MonthlyAssessments monthlyAssessments, @Param("userName") String userName);
}
