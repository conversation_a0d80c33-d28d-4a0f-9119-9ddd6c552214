package com.hz.quarterlyassessments.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;

import com.hz.examination.service.IExaminationPaperService;
import com.hz.quarterlyassessments.service.IMonthlyAssessmentMerchantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;

import com.hz.common.utils.StringUtils;
import com.hz.common.utils.SecurityUtils;
import org.springframework.transaction.annotation.Transactional;
import com.hz.quarterlyassessments.domain.MonthlyAssessments;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentStaff;
import com.hz.quarterlyassessments.mapper.QuarterlyAssessmentsMapper;
import com.hz.quarterlyassessments.mapper.MonthlyAssessmentHistoryMapper;
import com.hz.quarterlyassessments.domain.QuarterlyAssessments;
import com.hz.quarterlyassessments.service.IQuarterlyAssessmentsService;
import com.hz.common.core.domain.entity.SysRole;
import com.hz.system.mapper.SysRoleMapper;
import com.hz.quarterlyassessments.domain.dto.SaveSelectedStaffAndMerchantsRequest;
import com.hz.quarterlyassessments.service.IMonthlyAssessmentStaffService;
import com.hz.merchant.config.MerchantSelectionProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 季度考核Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class QuarterlyAssessmentsServiceImpl implements IQuarterlyAssessmentsService {
    private static final Logger log = LoggerFactory.getLogger(QuarterlyAssessmentsServiceImpl.class);

    @Autowired
    private QuarterlyAssessmentsMapper quarterlyAssessmentsMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private MonthlyAssessmentHistoryMapper monthlyAssessmentHistoryMapper;

    @Autowired
    private IExaminationPaperService examinationPaperService;

    @Autowired
    private IMonthlyAssessmentMerchantService monthlyAssessmentMerchantService;

    @Autowired
    private IMonthlyAssessmentStaffService monthlyAssessmentStaffService;

    @Autowired
    private MerchantSelectionProperties merchantSelectionProperties;

    /**
     * 查询季度考核
     *
     * @param id 季度考核主键
     * @return 季度考核
     */
    @Override
    public QuarterlyAssessments selectQuarterlyAssessmentsById(String id) {
        QuarterlyAssessments result = quarterlyAssessmentsMapper.selectQuarterlyAssessmentsById(id);

        // 转换 staffList 为 staffMap (roleName -> userName)
        if (result != null && result.getMonthlyAssessmentsList() != null) {
            int totalMerchantCount = 0; // 统计所有月度考核的商户总数

            for (MonthlyAssessments monthlyAssessment : result.getMonthlyAssessmentsList()) {
                if (monthlyAssessment.getStaffList() != null && !monthlyAssessment.getStaffList().isEmpty()) {
                    Map<String, String> staffMap = new HashMap<>();
                    for (MonthlyAssessmentStaff staff : monthlyAssessment.getStaffList()) {
                        if (staff.getRoleName() != null && staff.getUserName() != null) {
                            String roleName = staff.getRoleName();
                            String userName = staff.getUserName();

                            // 如果角色已经存在，则用逗号拼接多个用户名
                            if (staffMap.containsKey(roleName)) {
                                staffMap.put(roleName, staffMap.get(roleName) + "," + userName);
                            } else {
                                staffMap.put(roleName, userName);
                            }
                        }
                    }
                    monthlyAssessment.setStaffMap(staffMap);
                    // 清空原始的 staffList，只保留 staffMap
                    monthlyAssessment.setStaffList(null);
                }

                // 统计当前月度考核关联的商户数量
                if (monthlyAssessment.getId() != null && !monthlyAssessment.getId().trim().isEmpty()) {
                    try {
                        int monthlyMerchantCount = monthlyAssessmentMerchantService.countMerchantsByMonthlyAssessmentId(Long.valueOf(monthlyAssessment.getId()));
                        if (monthlyMerchantCount >= 0) {
                            totalMerchantCount += monthlyMerchantCount;
                            // 将商户数量设置到检查户数字段
                            monthlyAssessment.setInspectedHouseholds(Long.valueOf(monthlyMerchantCount));
                        }
                    } catch (NumberFormatException e) {
                        log.warn("月度考核ID格式错误: {}", monthlyAssessment.getId(), e);
                    } catch (Exception e) {
                        log.error("查询月度考核商户数量失败: {}", monthlyAssessment.getId(), e);
                    }
                }


            }

            // 设置总的关联商户数量作为 assessmentCount
            result.setAssessmentCount(totalMerchantCount);
        }

        return result;
    }

    /**
     * 查询季度考核列表
     *
     * @param quarterlyAssessments 季度考核
     * @return 季度考核
     */
    @Override
    public List<QuarterlyAssessments> selectQuarterlyAssessmentsList(QuarterlyAssessments quarterlyAssessments) {
        List<QuarterlyAssessments> list = quarterlyAssessmentsMapper.selectQuarterlyAssessmentsList(quarterlyAssessments);

        // 为每个季度考核设置考核数量
        for (QuarterlyAssessments quarterly : list) {
            int assessmentCount = examinationPaperService.countExaminationPaperByQuarterlyAssessmentId(quarterly.getId());
            quarterly.setAssessmentCount(assessmentCount);
        }

        return list;
    }

    /**
     * 新增季度考核
     *
     * @param quarterlyAssessments 季度考核
     * @return 结果
     */
    @Transactional
    @Override
    public int insertQuarterlyAssessments(QuarterlyAssessments quarterlyAssessments) {
        // 设置创建时间和更新时间
        Date now = new Date();
        quarterlyAssessments.setCreatedAt(now);
        quarterlyAssessments.setUpdatedAt(now);

        // 设置创建者信息
        try {
            Long currentUserId = SecurityUtils.getUserId();
            String currentUserName = SecurityUtils.getUsername();
            quarterlyAssessments.setCreateId(currentUserId);
            quarterlyAssessments.setCreateBy(currentUserName);
            quarterlyAssessments.setCreateTime(now);
        } catch (Exception e) {
            // 如果获取当前用户失败，使用默认值
        }

        // 自动设置年份和季度（如果未设置）
        if (StringUtils.isEmpty(quarterlyAssessments.getYear()) || quarterlyAssessments.getQuarter() == null) {
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            int currentMonth = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH 从0开始
            int currentQuarter = (currentMonth - 1) / 3 + 1;

            if (StringUtils.isEmpty(quarterlyAssessments.getYear())) {
                quarterlyAssessments.setYear(String.valueOf(currentYear));
            }
            if (quarterlyAssessments.getQuarter() == null) {
                quarterlyAssessments.setQuarter(Long.valueOf(currentQuarter));
            }
        }

        // 检查是否已存在相同年份和季度的季度考核
        QuarterlyAssessments existingAssessment = quarterlyAssessmentsMapper.checkYearQuarterExists(
                quarterlyAssessments.getYear(), quarterlyAssessments.getQuarter());
        if (existingAssessment != null) {
            // 返回特殊错误码 -1 表示重复
            return -1;
        }

        // 设置默认状态
        if (StringUtils.isEmpty(quarterlyAssessments.getStatus())) {
            quarterlyAssessments.setStatus("1"); // 1-已提交，2-进行中，3-未完成，4-已完成
        }

        // 插入季度考核记录，数据库会自动生成ID
        int rows = quarterlyAssessmentsMapper.insertQuarterlyAssessments(quarterlyAssessments);

        // 处理月度考核数据
        if (rows > 0) {
            // 优先使用前端传来的月度考核列表
            if (StringUtils.isNotNull(quarterlyAssessments.getMonthlyAssessmentsList())
                    && !quarterlyAssessments.getMonthlyAssessmentsList().isEmpty()) {
                insertMonthlyAssessments(quarterlyAssessments);
            } else {
                // 如果前端没有传月度考核列表，则自动生成
                generateMonthlyAssessments(quarterlyAssessments);
            }
        }

        return rows;
    }

    /**
     * 修改季度考核
     *
     * @param quarterlyAssessments 季度考核
     * @return 结果
     */
    @Transactional
    @Override
    public int updateQuarterlyAssessments(QuarterlyAssessments quarterlyAssessments) {
        quarterlyAssessments.setUpdatedAt(new Date());

        // 只更新季度考核的基本信息，不删除关联数据
        // 如果需要更新月度考核信息，应该通过专门的方法来处理
        return quarterlyAssessmentsMapper.updateQuarterlyAssessments(quarterlyAssessments);
    }

    @Override
    public int release(Long monthlyId, String status) {
        return quarterlyAssessmentsMapper.release(monthlyId, status);
    }

    /**
     * 批量删除季度考核
     *
     * @param ids 需要删除的季度考核主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteQuarterlyAssessmentsByIds(String[] ids) {
        // 按正确的顺序删除以避免外键约束错误
        for (String id : ids) {
            // 先获取该季度考核下的所有月度考核ID
            List<MonthlyAssessments> monthlyAssessmentsList = quarterlyAssessmentsMapper.selectMonthlyAssessmentsByQuarterlyId(id);

            // 删除examination_paper表中的数据
            for (MonthlyAssessments monthlyAssessment : monthlyAssessmentsList) {
                examinationPaperService.deleteExaminationPaperByMonthlyId(Long.valueOf(monthlyAssessment.getId()));
            }
        }

        // 删除monthly_assessment_merchants表中的数据
        for (String id : ids) {
            monthlyAssessmentMerchantService.deleteByQuarterlyAssessmentId(id);
        }

        quarterlyAssessmentsMapper.deleteMonthlyAssessmentStaffByQuarterlyAssessmentIds(ids);
        // 先删除月度考核历史记录
        for (String id : ids) {
            monthlyAssessmentHistoryMapper.deleteByQuarterlyAssessmentId(id);
        }
        // 再删除月度考核记录
        quarterlyAssessmentsMapper.deleteMonthlyAssessmentsByQuarterlyAssessmentIds(ids);
        return quarterlyAssessmentsMapper.deleteQuarterlyAssessmentsByIds(ids);
    }

    /**
     * 删除季度考核信息
     *
     * @param id 季度考核主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteQuarterlyAssessmentsById(String id) {
        // 按正确的顺序删除以避免外键约束错误
        // 先获取该季度考核下的所有月度考核ID
        List<MonthlyAssessments> monthlyAssessmentsList = quarterlyAssessmentsMapper.selectMonthlyAssessmentsByQuarterlyId(id);

        // 删除examination_paper表中的数据
        for (MonthlyAssessments monthlyAssessment : monthlyAssessmentsList) {
            examinationPaperService.deleteExaminationPaperByMonthlyId(Long.valueOf(monthlyAssessment.getId()));
        }

        // 删除monthly_assessment_merchants表中的数据
        monthlyAssessmentMerchantService.deleteByQuarterlyAssessmentId(id);

        quarterlyAssessmentsMapper.deleteMonthlyAssessmentStaffByQuarterlyAssessmentIds(new String[]{id});
        // 先删除月度考核历史记录
        monthlyAssessmentHistoryMapper.deleteByQuarterlyAssessmentId(id);
        // 再删除月度考核记录
        quarterlyAssessmentsMapper.deleteMonthlyAssessmentsByQuarterlyAssessmentId(id);
        return quarterlyAssessmentsMapper.deleteQuarterlyAssessmentsById(id);
    }


    /**
     * 自动生成当前季度的三个月数据
     *
     * @param quarterlyAssessments 季度考核对象
     */
    private void generateMonthlyAssessments(QuarterlyAssessments quarterlyAssessments) {
        List<MonthlyAssessments> monthlyAssessmentsList = new ArrayList<>();
        String quarterlyId = quarterlyAssessments.getId();
        Long quarter = quarterlyAssessments.getQuarter();
        String year = quarterlyAssessments.getYear();
        Date now = new Date();

        // 根据季度计算起始月份
        int startMonth = (quarter.intValue() - 1) * 3 + 1;

        // 生成三个月的数据
        for (int i = 0; i < 3; i++) {
            MonthlyAssessments monthlyAssessment = new MonthlyAssessments();
            // 设置业务ID：quarterlyAssessmentId + month 拼接
            Long monthValue = Long.valueOf(startMonth + i);
            String businessId = quarterlyId + String.format("%02d", monthValue); // 月份补零，如：2024001 + 01 = 202400101
            monthlyAssessment.setId(businessId);
            monthlyAssessment.setQuarterlyAssessmentId(quarterlyId);
            monthlyAssessment.setMonth(monthValue);
            monthlyAssessment.setName(year + "年第" + quarter + "季度第" + (i + 1) + "个月考核");
            monthlyAssessment.setStatus("1"); // 默认状态：已提交
            monthlyAssessment.setCreatedAt(now);
            monthlyAssessment.setUpdatedAt(now);


            // 设置创建者信息
            try {
                Long currentUserId = SecurityUtils.getUserId();
                String currentUserName = SecurityUtils.getUsername();
                monthlyAssessment.setCreateId(currentUserId);
                monthlyAssessment.setCreateBy(currentUserName);
                monthlyAssessment.setCreateTime(now);
            } catch (Exception e) {
                // 如果获取当前用户失败，使用plannerUserId作为备选
                if (StringUtils.isNotEmpty(quarterlyAssessments.getPlannerUserId())) {
                    try {
                        monthlyAssessment.setCreateId(Long.valueOf(quarterlyAssessments.getPlannerUserId()));
                    } catch (NumberFormatException ex) {
                        // 如果转换失败，忽略
                    }
                }
            }

            monthlyAssessmentsList.add(monthlyAssessment);
        }

        if (!monthlyAssessmentsList.isEmpty()) {
            quarterlyAssessmentsMapper.batchMonthlyAssessments(monthlyAssessmentsList);
        }
    }

    /**
     * 新增月度考核信息
     *
     * @param quarterlyAssessments 季度考核对象
     */
    @Transactional
    public void insertMonthlyAssessments(QuarterlyAssessments quarterlyAssessments) {
        List<MonthlyAssessments> monthlyAssessmentsList = quarterlyAssessments.getMonthlyAssessmentsList();
        String quarterlyId = quarterlyAssessments.getId();
        if (StringUtils.isNotNull(monthlyAssessmentsList)) {
            List<MonthlyAssessments> list = new ArrayList<MonthlyAssessments>();
            List<MonthlyAssessmentStaff> allStaffList = new ArrayList<MonthlyAssessmentStaff>();

            for (MonthlyAssessments monthlyAssessment : monthlyAssessmentsList) {
                // 检查是否有县区列表
                if (monthlyAssessment.getChildsList() != null && !monthlyAssessment.getChildsList().isEmpty()) {
                    // 为每个县区创建一个月度考核记录
                    for (Map<String, Object> district : monthlyAssessment.getChildsList()) {
                        MonthlyAssessments districtMonthlyAssessment = createDistrictMonthlyAssessment(
                            monthlyAssessment, quarterlyId, district);
                        list.add(districtMonthlyAssessment);
                    }
                } else {
                    // 如果没有县区列表，创建一个通用的月度考核记录
                    MonthlyAssessments generalMonthlyAssessment = createGeneralMonthlyAssessment(
                        monthlyAssessment, quarterlyId);
                    list.add(generalMonthlyAssessment);
                }
            }

            // 先插入月度考核记录
            if (list.size() > 0) {
                quarterlyAssessmentsMapper.batchMonthlyAssessments(list);

                // 查询刚插入的月度考核记录以获取ID和month
                QuarterlyAssessments tempQuarterly = new QuarterlyAssessments();
                tempQuarterly.setId(quarterlyId);
                List<MonthlyAssessments> insertedMonthlyList = quarterlyAssessmentsMapper.selectMonthlyAssessmentsByQuarterlyId(quarterlyId);

                // 构建月份到月度考核ID的映射
                Map<Integer, Integer> monthlyMap = new HashMap<>();
                for (MonthlyAssessments monthlyAssessment : insertedMonthlyList) {
                    try {
                        monthlyMap.put(monthlyAssessment.getMonth().intValue(), Integer.valueOf(monthlyAssessment.getId()));
                    } catch (NumberFormatException e) {
                        log.warn("月度考核ID无法转换为Integer: {}", monthlyAssessment.getId(), e);
                        // 如果ID不是数字，跳过这条记录
                        continue;
                    }
                }

                // 调用考试试卷服务
                if (!monthlyMap.isEmpty()) {
                    try {
                        Long currentUserId = SecurityUtils.getUserId();
                        String currentUserName = SecurityUtils.getUsername();
                        examinationPaperService.batchInsertExaminationPaper(monthlyMap, currentUserId, currentUserName);
                    } catch (Exception e) {
                        log.error("调用考试试卷服务失败", e);
                        throw new RuntimeException("批量插入考试试卷失败", e);
                    }
                }

                // 处理人员关联数据
                for (int i = 0; i < monthlyAssessmentsList.size(); i++) {
                    MonthlyAssessments monthlyAssessment = monthlyAssessmentsList.get(i);

                    // 需要通过month匹配找到对应的ID
                    String monthlyId = null;
                    for (MonthlyAssessments inserted : insertedMonthlyList) {
                        if (inserted.getMonth().equals(monthlyAssessment.getMonth())) {
                            monthlyId = inserted.getId();
                            break;
                        }
                    }

                    if (monthlyId == null) {
                        continue; // 如果找不到对应的ID，跳过这条记录
                    }

                    // 处理人员关联 -
                    if (StringUtils.isNotNull(monthlyAssessment.getStaffList()) && !monthlyAssessment.getStaffList().isEmpty()) {
                        // 使用新的中间表方式
                        for (MonthlyAssessmentStaff staff : monthlyAssessment.getStaffList()) {
                            if (StringUtils.isEmpty(staff.getId())) {
                                staff.setId(java.util.UUID.randomUUID().toString().replace("-", ""));
                            }
                            staff.setMonthlyAssessmentId(Long.valueOf(monthlyId));
                            if (staff.getCreatedAt() == null) {
                                staff.setCreatedAt(new Date());
                            }
                            if (staff.getUpdatedAt() == null) {
                                staff.setUpdatedAt(new Date());
                            }
                            allStaffList.add(staff);
                        }
                    }

                }

                // 批量插入人员关联数据
                if (!allStaffList.isEmpty()) {
                    quarterlyAssessmentsMapper.batchMonthlyAssessmentStaff(allStaffList);
                }
            }
        }
    }

    /**
     * 根据角色键值获取角色ID
     *
     * @param roleKey 角色键值
     * @return 角色ID
     */
    private Long getMonthlyAssessmentRoleId(String roleKey) {
        try {
            SysRole role = sysRoleMapper.checkRoleKeyUnique(roleKey);
            return role != null ? role.getRoleId() : null;
        } catch (Exception e) {
            // 如果查询失败，返回null
            return null;
        }
    }

    /**
     * 根据月度考核ID查询月度考核详情（包括人员配置）
     *
     * @param monthlyAssessmentId 月度考核ID
     * @return 月度考核详情
     */
    @Override
    public MonthlyAssessments selectMonthlyAssessmentWithStaffById(Long monthlyAssessmentId) {
        MonthlyAssessments result = quarterlyAssessmentsMapper.selectMonthlyAssessmentWithStaffById(String.valueOf(monthlyAssessmentId));

        if (result != null) {
            // 转换 staffList 为 staffMap (roleName -> userName)
            if (result.getStaffList() != null && !result.getStaffList().isEmpty()) {
                Map<String, String> staffMap = new HashMap<>();
                for (MonthlyAssessmentStaff staff : result.getStaffList()) {
                    if (staff.getRoleName() != null && staff.getUserName() != null) {
                        String roleName = staff.getRoleName();
                        String userName = staff.getUserName();

                        // 如果角色已经存在，则用逗号拼接多个用户名
                        if (staffMap.containsKey(roleName)) {
                            staffMap.put(roleName, staffMap.get(roleName) + "," + userName);
                        } else {
                            staffMap.put(roleName, userName);
                        }
                    }
                }
                result.setStaffMap(staffMap);
                // 清空原始的 staffList，只保留 staffMap
                result.setStaffList(null);
            }

            // 设置商户数量到检查户数字段
            try {
                int monthlyMerchantCount = monthlyAssessmentMerchantService.countMerchantsByMonthlyAssessmentId(monthlyAssessmentId);
                if (monthlyMerchantCount >= 0) {
                    result.setInspectedHouseholds(Long.valueOf(monthlyMerchantCount));
                }
            } catch (Exception e) {
                log.error("查询月度考核商户数量失败: {}", monthlyAssessmentId, e);
            }
        }

        return result;
    }


    /**
     * 保存抽取到的人员和商户
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public boolean saveSelectedStaffAndMerchants(SaveSelectedStaffAndMerchantsRequest request) {
        boolean staffResult = true;
        if (request.getStaffData() != null && request.getStaffData().getMonthlyAssessmentId() != null) {
            staffResult = monthlyAssessmentStaffService.saveSelectedStaff(
                    request.getStaffData().getMonthlyAssessmentId(),
                    request.getStaffData().getLeaders(),
                    request.getStaffData().getSupervisors(),
                    request.getStaffData().getMonopolyStaffs(),
                    request.getStaffData().getMarketingStaffs(),
                    request.getStaffData().getYear(),
                    request.getStaffData().getMonth()
            );
            log.info("人员数据保存结果: {}", staffResult);
        } else {
            log.info("没有人员数据需要保存，跳过人员保存操作");
        }

        boolean merchantResult = true;
        if (request.getMerchantData() != null && request.getMerchantData().getMonthlyAssessmentId() != null) {
            // 检查是否有实际的商户数据需要保存
            boolean hasMerchantData = request.getMerchantData().getSelectedMerchants() != null && 
                                    !request.getMerchantData().getSelectedMerchants().isEmpty();
            
            if (hasMerchantData) {
                merchantResult = monthlyAssessmentMerchantService.saveSelectedMerchants(
                        request.getMerchantData().getMonthlyAssessmentId(),
                        request.getMerchantData().getSelectedMerchants(),
                        request.getMerchantData().getSelectionType(),
                        request.getMerchantData().getTotalDistance()
                );
                log.info("商户数据保存结果: {}", merchantResult);
            } else {
                log.info("没有商户数据需要保存，跳过商户保存操作");
            }
        } else {
            log.info("没有商户数据传递，跳过商户保存操作");
        }

        // 处理JSON数据并更新到monthly_assessments表
        boolean jsonUpdateResult = true;
        if (request.getJsonData() != null && !request.getJsonData().trim().isEmpty()) {
            try {
                // 获取月度考核ID，优先从staffData获取，如果没有则从merchantData获取
                Long monthlyAssessmentId = null;
                if (request.getStaffData() != null && request.getStaffData().getMonthlyAssessmentId() != null) {
                    monthlyAssessmentId = request.getStaffData().getMonthlyAssessmentId();
                } else if (request.getMerchantData() != null && request.getMerchantData().getMonthlyAssessmentId() != null) {
                    monthlyAssessmentId = request.getMerchantData().getMonthlyAssessmentId();
                }

                if (monthlyAssessmentId != null) {
                    // 创建月度考核对象并设置JSON数据
                    MonthlyAssessments monthlyAssessments = new MonthlyAssessments();
                    monthlyAssessments.setId(monthlyAssessmentId.toString());
                    monthlyAssessments.setJsonData(request.getJsonData());
                    monthlyAssessments.setUpdatedAt(new Date());

                    // 更新月度考核记录
                    int updateResult = quarterlyAssessmentsMapper.updateMonthlyAssessments(monthlyAssessments);
                    jsonUpdateResult = updateResult > 0;
                    log.info("JSON数据更新结果: {}", jsonUpdateResult);
                }
            } catch (Exception e) {
                log.error("更新JSON数据失败", e);
                jsonUpdateResult = false;
            }
        }
        
        log.info("保存结果 - staffResult: {}, merchantResult: {}, jsonUpdateResult: {}", 
                staffResult, merchantResult, jsonUpdateResult);
        
        // 只有当所有操作都成功时（或者没有提供数据时），才返回true
        return staffResult && merchantResult && jsonUpdateResult;
    }


    /**
     * 分页查询所有月度考核详细信息（包括人员配置）
     *
     * @param monthlyAssessments 查询条件
     * @return 月度考核详细信息列表
     */
    @Override
    public List<MonthlyAssessments> selectMonthlyAssessmentsWithStaffList(MonthlyAssessments monthlyAssessments) {
        List<MonthlyAssessments> result = quarterlyAssessmentsMapper.selectMonthlyAssessmentsWithStaffList(monthlyAssessments);
        return processMonthlyAssessmentsResult(result);
    }

    /**
     * 分页查询指定用户参与的月度考核详细信息（包括人员配置）
     *
     * @param monthlyAssessments 查询条件
     * @param userName           用户名
     * @return 月度考核详细信息列表
     */
    @Override
    public List<MonthlyAssessments> selectMonthlyAssessmentsWithStaffListByUser(MonthlyAssessments monthlyAssessments, String userName) {
        List<MonthlyAssessments> result = quarterlyAssessmentsMapper.selectMonthlyAssessmentsWithStaffListByUser(monthlyAssessments, userName);
        return processMonthlyAssessmentsResult(result);
    }

    /**
     * 处理月度考核结果，转换staffList为staffMap并设置商户数量
     */
    private List<MonthlyAssessments> processMonthlyAssessmentsResult(List<MonthlyAssessments> result) {
        if (result != null && !result.isEmpty()) {
            for (MonthlyAssessments monthlyAssessment : result) {
                // 转换 staffList 为 staffMap (roleName -> userName)
                if (monthlyAssessment.getStaffList() != null && !monthlyAssessment.getStaffList().isEmpty()) {
                    Map<String, String> staffMap = new HashMap<>();
                    for (MonthlyAssessmentStaff staff : monthlyAssessment.getStaffList()) {
                        if (staff.getRoleName() != null && staff.getUserName() != null) {
                            String roleName = staff.getRoleName();
                            String userName = staff.getUserName();

                            // 如果角色已经存在，则用逗号拼接多个用户名
                            if (staffMap.containsKey(roleName)) {
                                staffMap.put(roleName, staffMap.get(roleName) + "," + userName);
                            } else {
                                staffMap.put(roleName, userName);
                            }
                        }
                    }
                    monthlyAssessment.setStaffMap(staffMap);
                    // 清空原始的 staffList，只保留 staffMap
                    monthlyAssessment.setStaffList(null);
                }

                // 设置商户数量到检查户数字段
                if (monthlyAssessment.getId() != null && !monthlyAssessment.getId().trim().isEmpty()) {
                    try {
                        int monthlyMerchantCount = monthlyAssessmentMerchantService.countMerchantsByMonthlyAssessmentId(Long.valueOf(monthlyAssessment.getId()));
                        if (monthlyMerchantCount >= 0) {
                            monthlyAssessment.setInspectedHouseholds(Long.valueOf(monthlyMerchantCount));
                        }
                    } catch (Exception e) {
                        log.error("查询月度考核商户数量失败: {}", monthlyAssessment.getId(), e);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 修改月度考核
     *
     * @param monthlyAssessments 月度考核
     * @return 结果
     */
    @Override
    public int updateMonthlyAssessments(MonthlyAssessments monthlyAssessments) {
        monthlyAssessments.setUpdatedAt(new Date());
        return quarterlyAssessmentsMapper.updateMonthlyAssessments(monthlyAssessments);
    }

    /**
     * 为特定县区创建月度考核记录
     *
     * @param template 模板月度考核对象
     * @param quarterlyId 季度考核ID
     * @param district 县区信息
     * @return 月度考核记录
     */
    private MonthlyAssessments createDistrictMonthlyAssessment(MonthlyAssessments template, String quarterlyId, Map<String, Object> district) {
        MonthlyAssessments monthlyAssessment = new MonthlyAssessments();
        Date now = new Date();

        // 设置业务ID：quarterlyAssessmentId + month + deptId 拼接
        Object deptIdObj = district.get("value");
        String deptId = deptIdObj != null ? deptIdObj.toString() : "";
        String businessId = quarterlyId + String.format("%02d", template.getMonth()) + deptId;
        monthlyAssessment.setId(businessId);

        // 设置基本信息
        monthlyAssessment.setQuarterlyAssessmentId(quarterlyId);
        monthlyAssessment.setMonth(template.getMonth());
        monthlyAssessment.setDeptId(deptId);

        // 设置名称，包含县区信息
        Object labelObj = district.get("label");
        String districtName = labelObj != null ? labelObj.toString() : "未知县区";
        monthlyAssessment.setName(template.getName() + " - " + districtName);

        // 复制其他属性
        monthlyAssessment.setAssessmentType(template.getAssessmentType());
        monthlyAssessment.setDistrict(template.getDistrict());
        monthlyAssessment.setInspectedHouseholds(template.getInspectedHouseholds());
        monthlyAssessment.setStatus(StringUtils.isEmpty(template.getStatus()) ? "1" : template.getStatus());

        // 设置时间
        monthlyAssessment.setCreatedAt(template.getCreatedAt() != null ? template.getCreatedAt() : now);
        monthlyAssessment.setUpdatedAt(template.getUpdatedAt() != null ? template.getUpdatedAt() : now);

        // 设置创建者信息
        setCreatorInfo(monthlyAssessment, template, now);

        return monthlyAssessment;
    }

    /**
     * 创建通用月度考核记录（无县区信息）
     *
     * @param template 模板月度考核对象
     * @param quarterlyId 季度考核ID
     * @return 月度考核记录
     */
    private MonthlyAssessments createGeneralMonthlyAssessment(MonthlyAssessments template, String quarterlyId) {
        MonthlyAssessments monthlyAssessment = new MonthlyAssessments();
        Date now = new Date();

        // 设置业务ID：quarterlyAssessmentId + month 拼接
        String businessId = quarterlyId + String.format("%02d", template.getMonth());
        monthlyAssessment.setId(businessId);

        // 设置基本信息
        monthlyAssessment.setQuarterlyAssessmentId(quarterlyId);
        monthlyAssessment.setMonth(template.getMonth());
        monthlyAssessment.setName(template.getName());
        monthlyAssessment.setAssessmentType(template.getAssessmentType());
        monthlyAssessment.setDistrict(template.getDistrict());
        monthlyAssessment.setInspectedHouseholds(template.getInspectedHouseholds());
        monthlyAssessment.setStatus(StringUtils.isEmpty(template.getStatus()) ? "1" : template.getStatus());

        // 设置时间
        monthlyAssessment.setCreatedAt(template.getCreatedAt() != null ? template.getCreatedAt() : now);
        monthlyAssessment.setUpdatedAt(template.getUpdatedAt() != null ? template.getUpdatedAt() : now);

        // 设置创建者信息
        setCreatorInfo(monthlyAssessment, template, now);

        return monthlyAssessment;
    }

    /**
     * 设置创建者信息
     *
     * @param monthlyAssessment 月度考核对象
     * @param template 模板对象
     * @param now 当前时间
     */
    private void setCreatorInfo(MonthlyAssessments monthlyAssessment, MonthlyAssessments template, Date now) {
        if (template.getCreateId() == null || StringUtils.isEmpty(template.getCreateBy())) {
            try {
                Long currentUserId = SecurityUtils.getUserId();
                String currentUserName = SecurityUtils.getUsername();
                monthlyAssessment.setCreateId(currentUserId);
                monthlyAssessment.setCreateBy(currentUserName);
                monthlyAssessment.setCreateTime(template.getCreateTime() != null ? template.getCreateTime() : now);
            } catch (Exception e) {
                // 如果获取当前用户失败，忽略
            }
        } else {
            monthlyAssessment.setCreateId(template.getCreateId());
            monthlyAssessment.setCreateBy(template.getCreateBy());
            monthlyAssessment.setCreateTime(template.getCreateTime());
        }
    }
}
