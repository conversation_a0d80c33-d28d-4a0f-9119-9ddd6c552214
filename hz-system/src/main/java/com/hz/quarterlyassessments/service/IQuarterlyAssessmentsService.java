package com.hz.quarterlyassessments.service;

import java.util.List;
import com.hz.quarterlyassessments.domain.QuarterlyAssessments;
import com.hz.quarterlyassessments.domain.MonthlyAssessments;
import com.hz.quarterlyassessments.domain.dto.SaveSelectedStaffAndMerchantsRequest;

/**
 * 季度考核Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IQuarterlyAssessmentsService 
{
    /**
     * 查询季度考核
     * 
     * @param id 季度考核主键
     * @return 季度考核
     */
    public QuarterlyAssessments selectQuarterlyAssessmentsById(String id);

    /**
     * 查询季度考核列表
     * 
     * @param quarterlyAssessments 季度考核
     * @return 季度考核集合
     */
    public List<QuarterlyAssessments> selectQuarterlyAssessmentsList(QuarterlyAssessments quarterlyAssessments);

    /**
     * 新增季度考核
     * 
     * @param quarterlyAssessments 季度考核
     * @return 结果
     */
    public int insertQuarterlyAssessments(QuarterlyAssessments quarterlyAssessments);

    /**
     * 修改季度考核
     * 
     * @param quarterlyAssessments 季度考核
     * @return 结果
     */
    public int updateQuarterlyAssessments(QuarterlyAssessments quarterlyAssessments);

    public int release(Long monthlyId, String status);

    /**
     * 批量删除季度考核
     * 
     * @param ids 需要删除的季度考核主键集合
     * @return 结果
     */
    public int deleteQuarterlyAssessmentsByIds(String[] ids);

    /**
     * 删除季度考核信息
     * 
     * @param id 季度考核主键
     * @return 结果
     */
    public int deleteQuarterlyAssessmentsById(String id);

    /**
     * 根据月度考核ID查询月度考核详情（包括人员配置）
     *
     * @param monthlyAssessmentId 月度考核ID
     * @return 月度考核详情
     */
    public MonthlyAssessments selectMonthlyAssessmentWithStaffById(String monthlyAssessmentId);

    /**
     * 同时保存抽取的人员和商户
     *
     * @param request 包含人员和商户数据的请求
     * @return 结果
     */
    public boolean saveSelectedStaffAndMerchants(SaveSelectedStaffAndMerchantsRequest request);

    /**
     * 分页查询所有月度考核详细信息（包括人员配置）
     * 
     * @param monthlyAssessments 查询条件
     * @return 月度考核详细信息列表
     */
    public List<MonthlyAssessments> selectMonthlyAssessmentsWithStaffList(MonthlyAssessments monthlyAssessments);

    /**
     * 分页查询指定用户参与的月度考核详细信息（包括人员配置）
     * 
     * @param monthlyAssessments 查询条件
     * @param userName 用户名
     * @return 月度考核详细信息列表
     */
    public List<MonthlyAssessments> selectMonthlyAssessmentsWithStaffListByUser(MonthlyAssessments monthlyAssessments, String userName);

    /**
     * 修改月度考核
     * 
     * @param monthlyAssessments 月度考核
     * @return 结果
     */
    public int updateMonthlyAssessments(MonthlyAssessments monthlyAssessments);
}
