<template>
    <div class="app-container">
        <div class="store_content">
            <div class="store_content_header">
                <div class="title">{{ form.name }}</div>
            </div>
            <el-form class="store_content_form" ref="quarterlyassessmentsRef" :model="form" label-width="auto">
                <el-row>
                    <el-col :span="3">
                        <el-form-item label="所属年份:" prop="year">
                            <span style="color: #333;font-weight: bold;">{{ form.year?.substring(0, 4) }}年</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="所属季度:" prop="quarter">
                            <span style="color: #333;font-weight: bold;">第{{ form.quarter }}季度</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div class="from_container" style="padding: 35px 30px;">
            <el-tabs v-model="activeTab" style="margin-bottom: 20px" v-if="monthlyAssessmentsList.length > 0">
                <!-- 现场考核页签 -->
                <el-tab-pane label="现场考核" name="onsite">
                    <sceneTable :monthlyAssessmentsList="monthlyAssessmentsList" @handleUpdate="handleUpdate">
                    </sceneTable>
                </el-tab-pane>
                <!-- 目标考核页签 -->
                <el-tab-pane label="目标考核" name="target">
                    <targetTable></targetTable>
                </el-tab-pane>
                <!-- 汇总页签 -->
                <el-tab-pane label="汇总" name="summary">
                    <summaryTable></summaryTable>
                </el-tab-pane>
            </el-tabs>
            <el-empty v-if="monthlyAssessmentsList.length === 0 && form.year && form.quarter"
                description="请选择年份和季度以生成月度考核预览" :image-size="80" />
        </div>
        <div class="dialog-footer">
            <el-button class="upload_cancel" @click="cancel">返 回</el-button>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import cache from '@/plugins/cache'
import { getQuarterlyassessments, updateQuarterlyassessments, release, unRelease } from "@/api/quarterlyassessments/quarterlyassessments"
import sceneTable from './components/sceneTable.vue'
import targetTable from './components/targetTable.vue'
import summaryTable from './components/summaryTable.vue'
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const form = ref({})
const monthlyAssessmentsList = ref([])
const activeTab = ref("onsite")
const id = route.query.id;

onMounted(() => {
    getList()
})
const getList = () => {
    getQuarterlyassessments(id).then(response => {
        form.value = response.data
        monthlyAssessmentsList.value = response.data.monthlyAssessmentsList || []
    })
}
// 修改
const handleUpdate = (row, type) => {
    const query = {
        id: row.id,
        year: form.value.year,
        month: row.month,
        name: row.name,
        quarterId: id,
        jsonData: row.jsonData,
        inspectedHouseholds: row.inspectedHouseholds,
        createTime: row.createTime,
        inspectedHouseholds: row.inspectedHouseholds,
        staffMap: row.staffMap
    }
    cache.session.setJSON('configData', query)
    switch (type) {
        case 'up':
            router.push({
                path: '/assessment/quarterlyassessments/childs/onSiteAssessment'
            })
            break
        case 'county':
            router.push({
                path: '/assessment/quarterlyassessments/childs/countyPerformance'
            })
            break
        case 'merchant':
            router.push({
                path: '/assessment/quarterlyassessments/childs/merchantPerformance'
            })
            break
        case 'release':
            ElMessageBox.confirm('是否确认发布？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                release(row.id).then(res => {
                    if (res.code == 200) {
                        getList()
                        ElMessage.success('发布成功')
                    }
                })
            })
            break
        case 'unRelease':
            ElMessageBox.confirm('是否撤销发布？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                unRelease(row.id).then(res => {
                    if (res.code == 200) {
                        getList()
                        ElMessage.success('撤销发布成功')
                    }
                })
            })
            break
    }
}

const cancel = () => {
    const obj = { path: "/assessment/quarterlyassessments" }
    proxy.$tab.closeOpenPage(obj)
}
</script>

<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_content {
    box-sizing: border-box;
    padding: 20px 30px 0 30px;
    background-color: #fff;
    border-radius: 5px;
    margin-top: 10px;

    .store_content_header {
        display: flex;
        align-items: center;

        .title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }
    }

    .store_content_form {
        margin-top: 25px;
    }
}

:deep(.el-tabs) {
    .el-tabs__item {
        font-size: 22px;
        font-weight: bold;
        padding-bottom: 15px;
    }

    .el-tabs__nav-wrap:after {
        display: none;
    }

    .el-tabs__active-bar {
        background-color: #049B01;
        height: 4px;
        border-radius: 5px;
    }

    .el-tabs__item.is-active,
    .el-tabs__item:hover {
        color: #049B01;
    }
}

.from_container {
    min-height: calc(100vh - 600px);
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 30px;
    background-color: #fff;
    margin-top: 10px;

    .upload_submit,
    .upload_cancel {
        box-sizing: border-box;
        width: 140px;
        height: 45px;
        font-size: 16px;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
    }

    .upload_submit {
        color: #fff;
        background-color: #21A042;
    }

    .upload_cancel {
        border: 1px solid #21A042;
        color: #21A042;
    }
}
</style>